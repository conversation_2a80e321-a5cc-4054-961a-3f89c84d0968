import type { NextConfig } from "next";

const env = process.env.NODE_ENV || 'development';

const CONFIG = {
  test: {
    mobile_site_host: 'https://m-test.smartdeer.work',
    pc_site_host: 'https://www-test.smartdeer.work'
  },
  production: {
    mobile_site_host: 'https://m.smartdeer.work',
    pc_site_host: 'https://www.smartdeer.work'
  },
  development: {
    mobile_site_host: '',
    pc_site_host: ''
  }
};

const nextConfig: NextConfig = {
  // 基础配置
  reactStrictMode: true,
  
  // 图片优化配置
  images: {
    disableStaticImages: true,
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.smartdeer.work',
      },
      {
        protocol: 'https',
        hostname: 'm.smartdeer.work',
      },
      {
        protocol: 'https',
        hostname: 'static.smartdeer.com',
      }
    ],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 环境变量
  env: {
    MOBILE_SITE_HOST: CONFIG[env as keyof typeof CONFIG]?.mobile_site_host || '',
    PC_SITE_HOST: CONFIG[env as keyof typeof CONFIG]?.pc_site_host || '',
  },

  // 重定向配置
  async redirects() {
    return [
      // 移动端重定向逻辑移除，改为响应式设计
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // 编译配置
  compiler: {
    removeConsole: env === 'production',
  },

  // 实验性功能
  experimental: {
    scrollRestoration: true,
  },

  // 输出配置
  output: 'standalone',
  
  // Webpack 配置
  webpack: (config, { isServer }) => {
    // 优化配置
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    
    return config;
  },
};

export default nextConfig;
