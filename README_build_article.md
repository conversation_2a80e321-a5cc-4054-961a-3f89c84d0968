# Next.js 文章构建脚本

这个脚本用于从WordPress GraphQL API获取文章数据，并生成对应的Next.js页面组件。

## 功能特性

- 从WordPress GraphQL API获取文章数据
- 根据文章分类生成不同类型的页面（标准、带国旗、无横幅）
- 支持多语言（中文、英文、日文）
- 生成Next.js App Router格式的页面组件
- 自动创建目录结构
- 生成文章列表JSON文件

## 文件结构

```
build_article_nextjs.js          # 主构建脚本
templates/                       # Next.js模板文件
├── article-detail-template.tsx      # 标准文章模板
├── article-detail-flag-template.tsx # 带国旗的文章模板
└── article-detail-nobanner-template.tsx # 无横幅的文章模板
src/components/ArticleDetailClient.tsx   # 客户端交互组件
```

## 使用方法

### 1. 安装依赖

```bash
npm install graphql-request
```

### 2. 运行构建脚本

```bash
node build_article_nextjs.js
```

### 3. 生成的文件结构

脚本会在以下目录生成文章页面：

```
src/app/
├── zh/
│   ├── countries/[article-name]/page.tsx
│   ├── articles/[article-name]/page.tsx
│   └── marketing/[article-name]/page.tsx
├── en/
│   ├── countries/[article-name]/page.tsx
│   ├── articles/[article-name]/page.tsx
│   └── marketing/[article-name]/page.tsx
└── ja/
    ├── countries/[article-name]/page.tsx
    ├── articles/[article-name]/page.tsx
    └── marketing/[article-name]/page.tsx
```

同时会生成文章列表JSON文件：

```
src/data/articles/
├── countries/
│   ├── article-list-zh.json
│   ├── article-list-en.json
│   └── article-list-ja.json
├── articles/
│   ├── article-list-zh.json
│   ├── article-list-en.json
│   └── article-list-ja.json
└── marketing/
    ├── article-list-zh.json
    ├── article-list-en.json
    └── article-list-ja.json
```

## 文章分类映射

| 分类ID | 类型 | 语言 | 输出目录 | 模板类型 |
|--------|------|------|----------|----------|
| 4 | Country Guide | 中文 | zh/countries | Flag |
| 5 | Country Guide | 英文/日文 | en/countries, ja/countries | Flag |
| 7 | Articles | 英文/日文 | en/articles, ja/articles | No Banner |
| 8 | Articles | 中文 | zh/articles | No Banner |
| 10 | Marketing | 英文/日文 | en/marketing, ja/marketing | Standard |
| 11 | Marketing | 中文 | zh/marketing | Standard |

## 模板变量

模板文件中使用以下变量进行替换：

- `${TITLE}` - 文章标题
- `${DESCRIPTION}` - 文章描述
- `${SITE_NAME}` - 站点名称
- `${FEATURE_IMAGE_URL}` - 特色图片URL
- `${COUNTRY_FLAG_IMAGE_URL}` - 国旗图片URL
- `${CONTENT}` - 文章内容
- `${LANGUAGE}` - 语言代码（如：zh-CN）
- `${LANGUAGE_SIMPLE}` - 简化语言代码（如：zh）
- `${FORM_CONFIRM_PROMPT}` - 表单确认提示信息

## 自定义配置

### 修改API端点

在 `build_article_nextjs.js` 中修改：

```javascript
const endpoint = 'https://your-wordpress-site.com/graphql';
```

### 修改输出目录

在 `OUTPUT_DIRS` 对象中修改目录结构：

```javascript
const OUTPUT_DIRS = {
  zh: {
    countries: 'src/app/zh/countries',
    // ... 其他目录
  }
};
```

### 自定义模板

1. 在 `templates/` 目录中创建新的模板文件
2. 在脚本中引用新模板
3. 根据需要修改变量替换逻辑

## 注意事项

1. 确保WordPress站点已安装并配置了GraphQL插件
2. 确保有足够的权限访问GraphQL API
3. 生成的文件会覆盖现有同名文件
4. 建议在运行脚本前备份现有文件
5. 确保Next.js项目中已正确配置相关组件和样式

## 故障排除

### GraphQL请求失败
- 检查API端点是否正确
- 确认网络连接
- 验证GraphQL查询语法

### 文件生成失败
- 检查目录权限
- 确认模板文件存在
- 验证文件路径

### 模板变量未替换
- 检查模板文件中的变量语法
- 确认数据源中包含相应字段
- 验证替换逻辑

## 扩展功能

可以根据需要扩展以下功能：

1. 添加更多文章分类支持
2. 实现增量更新（只更新修改过的文章）
3. 添加图片优化和本地化
4. 集成到CI/CD流程
5. 添加错误处理和重试机制
