const fs = require('fs');
const path = require('path');
const { GraphQLClient } = require("graphql-request");

const endpoint = 'https://blog.smartdeer.work/graphql';

const articleGuideListZh = [];
const articleGuideListEn = [];
const articleGuideListJa = [];
const articleListZh = [];
const articleListEn = [];
const articleListJa = [];
const articleMarketingListZh = [];
const articleMarketingListEn = [];
const articleMarketingListJa = [];

let hasNextPage = true;
let afterArticleId = '';

// Next.js 目录结构
const OUTPUT_DIRS = {
  zh: {
    countries: 'src/app/zh/countries',
    articles: 'src/app/zh/articles',
    marketing: 'src/app/zh/marketing'
  },
  en: {
    countries: 'src/app/en/countries',
    articles: 'src/app/en/articles',
    marketing: 'src/app/en/marketing'
  },
  ja: {
    countries: 'src/app/ja/countries',
    articles: 'src/app/ja/articles',
    marketing: 'src/app/ja/marketing'
  }
};

// 确保目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 初始化目录结构
function initializeDirectories() {
  Object.values(OUTPUT_DIRS).forEach(langDirs => {
    Object.values(langDirs).forEach(dir => {
      ensureDirectoryExists(dir);
    });
  });
}

function getArticleFromServer() {
  const query = buildGraphql({
    orderby: {
      field: 'DATE',
      order: 'DESC'
    },
  }, afterArticleId, 10);
  
  const client = new GraphQLClient(endpoint, {});
  
  client.request(query).then(data => {
    console.log('✅ Fetched articles:', data.posts.nodes.length);
    const posts = data.posts.nodes;

    // 生成文章详情页
    posts.forEach(post => {
      try {
        generateArticleDetail(post);
      } catch (error) {
        console.error(`❌ Error generating article ${post.id}:`, error.message);
      }
    });

    hasNextPage = data.posts.pageInfo.hasNextPage;
    afterArticleId = data.posts.pageInfo.endCursor;

    if (hasNextPage) {
      console.log(`🔄 Fetching next batch... (cursor: ${afterArticleId})`);
      setTimeout(() => getArticleFromServer(), 1000); // 添加延迟避免API限制
    } else {
      generateArticleListFiles();
      console.log('🎉 Article generation completed successfully!');
    }
  }).catch(error => {
    console.error("💥 Fetch Error: ", error.message);
    console.error("Stack trace:", error.stack);

    // 添加重试逻辑
    if (!error.retryCount) error.retryCount = 0;
    if (error.retryCount < 3) {
      error.retryCount++;
      console.log(`🔄 Retrying... (attempt ${error.retryCount}/3)`);
      setTimeout(() => getArticleFromServer(), 2000 * error.retryCount);
    } else {
      console.error("❌ Max retries reached. Stopping execution.");
      hasNextPage = false;
      afterArticleId = '';
    }
  });
}

// 安全的内容清理函数
function sanitizeContent(content) {
  if (!content) return '';

  return content
    .replace(/\r\n/g, '\\n')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\n')
    .replace(/'/g, "\\'")
    .replace(/"/g, '\\"')
    .replace(/`/g, '\\`')
    .trim();
}

// 安全的模板字符串转义
function escapeForTemplate(str) {
  if (!str) return '';
  return str.replace(/'/g, "\\'").replace(/"/g, '\\"');
}

function generateArticleDetail(post) {
  // 验证必要字段
  if (!post || !post.countryGuideExternal?.fileName) {
    console.warn(`⚠️ Skipping post ${post?.id || 'unknown'}: missing required fields`);
    return;
  }

  // 安全清理内容
  if (post.content) {
    post.content = sanitizeContent(post.content);
  }

  // 读取Next.js模板文件
  const templates = {
    default: fs.readFileSync('./templates/article-detail-template.tsx', 'utf8'),
    flag: fs.readFileSync('./templates/article-detail-flag-template.tsx', 'utf8'),
    nobanner: fs.readFileSync('./templates/article-detail-nobanner-template.tsx', 'utf8')
  };

  const articleListItemData = {
    id: post.id,
    title: post.title,
    image: post.countryGuideExternal.listingImage,
    countryName: post.countryGuideExternal.countryName
  };

  post.categories.nodes.forEach(item => {
    let template = templates.default;
    
    // 根据分类选择模板
    if (item.categoryId === 4 || item.categoryId === 5) {
      template = templates.flag;
    } else if (item.categoryId === 7 || item.categoryId === 8) {
      template = templates.nobanner;
    }

    // 生成各语言版本
    const languages = [
      { categoryId: [4, 8, 11], lang: 'zh', locale: 'zh-CN', confirmPrompt: '您的请求已收到，我们会尽快与您联系。' },
      { categoryId: [5, 7, 10], lang: 'en', locale: 'en-US', confirmPrompt: 'We have received your request and we will contact with you as soon as possible.' },
      { categoryId: [5, 7, 10], lang: 'ja', locale: 'ja-JP', confirmPrompt: 'お問い合わせを受け付けました。できるだけ早くご連絡いたします。' }
    ];

    languages.forEach(({ categoryId, lang, locale, confirmPrompt }) => {
      if (categoryId.includes(item.categoryId)) {
        // 安全的模板替换
        const templateData = {
          '${TITLE}': escapeForTemplate(post.title || ''),
          '${DESCRIPTION}': escapeForTemplate(post.seo?.opengraphDescription || ''),
          '${SITE_NAME}': escapeForTemplate(post.seo?.opengraphSiteName || ''),
          '${FEATURE_IMAGE_URL}': post.featuredImage?.node?.sourceUrl || '',
          '${COUNTRY_FLAG_IMAGE_URL}': post.countryGuideExternal?.countryFlagImage?.node?.sourceUrl || '',
          '${CONTENT}': post.content || '',
          '${LANGUAGE}': locale,
          '${LANGUAGE_SIMPLE}': lang,
          '${FORM_CONFIRM_PROMPT}': escapeForTemplate(confirmPrompt)
        };

        let content = template;
        Object.entries(templateData).forEach(([placeholder, value]) => {
          const escapedPlaceholder = placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          content = content.replace(new RegExp(escapedPlaceholder, 'g'), value);
        });

        // 确定输出路径和文件名
        let outputDir, listArray;
        const fileName = post.countryGuideExternal.fileName;
        
        if (item.categoryId === 4 || item.categoryId === 5) {
          // Country Guide
          outputDir = OUTPUT_DIRS[lang].countries;
          listArray = lang === 'zh' ? articleGuideListZh : lang === 'en' ? articleGuideListEn : articleGuideListJa;
          listArray.push({
            ...articleListItemData,
            link: `/${lang}/countries/${fileName}`
          });
        } else if (item.categoryId === 7 || item.categoryId === 8) {
          // Articles
          outputDir = OUTPUT_DIRS[lang].articles;
          listArray = lang === 'zh' ? articleListZh : lang === 'en' ? articleListEn : articleListJa;
          listArray.push({
            ...articleListItemData,
            link: `/${lang}/articles/${fileName}`
          });
        } else if (item.categoryId === 10 || item.categoryId === 11) {
          // Marketing
          outputDir = OUTPUT_DIRS[lang].marketing;
          listArray = lang === 'zh' ? articleMarketingListZh : lang === 'en' ? articleMarketingListEn : articleMarketingListJa;
          listArray.push({
            ...articleListItemData,
            link: `/${lang}/marketing/${fileName}`
          });
        }

        if (outputDir && fileName) {
          try {
            // 创建文章目录
            const articleDir = path.join(outputDir, fileName);
            ensureDirectoryExists(articleDir);

            // 写入 page.tsx 文件
            const pagePath = path.join(articleDir, 'page.tsx');
            fs.writeFileSync(pagePath, content, 'utf8');

            console.log(`✅ Generated: ${pagePath}`);
          } catch (error) {
            console.error(`❌ Error writing file for ${fileName}:`, error.message);
          }
        } else {
          console.warn(`⚠️ Skipping article: missing outputDir (${outputDir}) or fileName (${fileName})`);
        }
      }
    });
  });
}

function generateArticleListFiles() {
  console.log('📝 Generating article list files...');

  // 生成文章列表JSON文件
  const lists = [
    { data: articleGuideListZh, path: 'src/data/articles/countries/article-list-zh.json' },
    { data: articleGuideListEn, path: 'src/data/articles/countries/article-list-en.json' },
    { data: articleGuideListJa, path: 'src/data/articles/countries/article-list-ja.json' },
    { data: articleListZh, path: 'src/data/articles/articles/article-list-zh.json' },
    { data: articleListEn, path: 'src/data/articles/articles/article-list-en.json' },
    { data: articleListJa, path: 'src/data/articles/articles/article-list-ja.json' },
    { data: articleMarketingListZh, path: 'src/data/articles/marketing/article-list-zh.json' },
    { data: articleMarketingListEn, path: 'src/data/articles/marketing/article-list-en.json' },
    { data: articleMarketingListJa, path: 'src/data/articles/marketing/article-list-ja.json' }
  ];

  lists.forEach(({ data, path: filePath }) => {
    try {
      ensureDirectoryExists(path.dirname(filePath));
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
      console.log(`✅ Generated list: ${filePath} (${data.length} articles)`);
    } catch (error) {
      console.error(`❌ Error generating list ${filePath}:`, error.message);
    }
  });

  // 输出统计信息
  const totalArticles = lists.reduce((sum, { data }) => sum + data.length, 0);
  console.log(`📊 Total articles generated: ${totalArticles}`);
}

function buildGraphql(where, after, limit) {
  let condition = 'first: ' + limit;
  condition += ", ";
  
  if (after) {
    condition += 'after: "' + after + '"';
    condition += ", ";
  }
  
  let whereJsonString = JSON.stringify(where);
  condition += "where: " + whereJsonString.replace(/"/g, '');
  
  return `
query articleQuery {
  posts(${condition}) {
    nodes {
      id
      title
      slug
      categories {
        nodes {
          categoryId
        }
      }
      seo {
        metaDesc
        metaKeywords
        opengraphDescription
        opengraphSiteName
        opengraphType
        title
      }
      featuredImage {
        node {
          sourceUrl(size: LARGE)
        }
      }
      countryGuideExternal {
        listingImage {
          node {
            sourceUrl(size: LARGE)
          }
        }
        countryFlagImage {
          node {
            sourceUrl(size: LARGE)
          }
        }
        countryName
        fileName
      }
      content
    }
    pageInfo {
      startCursor
      hasPreviousPage
      hasNextPage
      endCursor
    }
  }
}
`;
}

// 主执行函数
function main() {
  console.log('🚀 Starting Next.js article generation...');
  console.log(`📊 Configuration: endpoint=${endpoint}`);
  console.log(`📁 Output directories initialized`);

  try {
    initializeDirectories();
    getArticleFromServer();
  } catch (error) {
    console.error('💥 Fatal error during initialization:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  main,
  generateArticleDetail,
  generateArticleListFiles
};
