import type { NextApiRequest, NextApiResponse } from "next";
import sharp from "sharp";
import path from "path";
import fs from "fs";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { filename } = req.query;

    if (!filename || Array.isArray(filename)) {
      return res.status(400).json({ error: "Invalid filename" });
    }

    const filePath = path.join(process.cwd(), "public", filename);
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: "File not found" });
    }

    // 使用 sharp 裁掉透明边缘
    const buffer = await sharp(filePath)
      .trim() // 自动去掉透明边缘
      .toFormat("png")
      .toBuffer();

    res.setHeader("Content-Type", "image/png");
    res.send(buffer);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Image processing failed" });
  }
}
