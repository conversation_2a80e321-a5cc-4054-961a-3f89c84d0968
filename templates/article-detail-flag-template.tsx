import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import { Locale } from '@/types';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';
import ArticleDetailClient from '@/components/ArticleDetailClient';

interface ArticlePageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return generateSeoMetadata({
    title: '${TITLE}',
    description: '${DESCRIPTION}',
    openGraph: {
      title: '${TITLE}',
      description: '${DESCRIPTION}',
      siteName: '${SITE_NAME}',
      images: [
        {
          url: '${FEATURE_IMAGE_URL}',
          width: 1200,
          height: 630,
          alt: '${TITLE}'
        }
      ]
    }
  }, locale);
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { locale } = await params;

  const articleData = {
    title: '${TITLE}',
    content: '${CONTENT}',
    bannerImage: '${FEATURE_IMAGE_URL}',
    flagImage: '${COUNTRY_FLAG_IMAGE_URL}',
    locale: '${LANGUAGE}',
    localeSimple: '${LANGUAGE_SIMPLE}',
    confirmPrompt: '${FORM_CONFIRM_PROMPT}'
  };

  return (
    <div className="contries-page">
      {/* Header Section */}
      <header className="article-header">
        <div className="countries">
          <div className="countries-banner">
            <div className="banner">
              {articleData.bannerImage && (
                <figure>
                  <Image
                    src={articleData.bannerImage}
                    alt={articleData.title}
                    width={1200}
                    height={600}
                    className="banner-image"
                  />
                </figure>
              )}
            </div>

            {/* Country Flag */}
            <div className="flag">
              {articleData.flagImage && (
                <figure>
                  <Image
                    src={articleData.flagImage}
                    alt={`${articleData.title} Flag`}
                    width={200}
                    height={150}
                    className="flag-image"
                  />
                </figure>
              )}
            </div>
          </div>
          
          <div className="countries-content">
            <h1 className="article-title">{articleData.title}</h1>
            
            {/* Contact Form Box */}
            <div className="form-box">
              <ArticleDetailClient 
                locale={locale}
                confirmPrompt={articleData.confirmPrompt}
              />
            </div>
            
            {/* Article Content */}
            <div 
              className="article-content"
              dangerouslySetInnerHTML={{ __html: articleData.content }}
            />
          </div>
        </div>
      </header>
    </div>
  );
}
