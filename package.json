{"name": "smartdeer-website", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build:production": "NODE_ENV=production next build", "build:test": "NODE_ENV=test next build", "seo:report": "node scripts/seo-monitor.js", "seo:push-baidu": "node scripts/baidu-push.js", "seo:validate": "npm run seo:report && echo 'SEO validation completed'", "build:seo": "npm run seo:validate && npm run build:production"}, "dependencies": {"@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.0", "@types/video.js": "^7.3.58", "graphql-request": "^6.1.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.48.0", "react-intersection-observer": "^9.5.0", "sharp": "^0.33.0", "video.js": "^8.23.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@types/ejs": "^3.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.4.2", "framer-motion": "^12.23.6", "postcss": "^8.4.32", "postcss-import": "^16.1.1", "sass": "^1.69.0", "tailwindcss": "^3.4.0", "typescript": "^5"}}