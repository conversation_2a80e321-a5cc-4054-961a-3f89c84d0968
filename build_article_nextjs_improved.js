const fs = require('fs');
const path = require('path');
const { GraphQLClient } = require("graphql-request");

// 配置常量
const CONFIG = {
  endpoint: 'https://blog.smartdeer.work/graphql',
  maxRetries: 3,
  retryDelay: 1000,
  batchSize: 10
};

// 类型定义（JSDoc风格）
/**
 * @typedef {Object} ArticleData
 * @property {string} id
 * @property {string} title
 * @property {string} content
 * @property {Object} seo
 * @property {Object} featuredImage
 * @property {Object} countryGuideExternal
 * @property {Array} categories
 */

class ArticleGenerator {
  constructor() {
    this.articleLists = {
      zh: { guide: [], articles: [], marketing: [] },
      en: { guide: [], articles: [], marketing: [] },
      ja: { guide: [], articles: [], marketing: [] }
    };
    
    this.outputDirs = {
      zh: {
        countries: 'src/app/zh/countries',
        articles: 'src/app/zh/articles',
        marketing: 'src/app/zh/marketing'
      },
      en: {
        countries: 'src/app/en/countries',
        articles: 'src/app/en/articles',
        marketing: 'src/app/en/marketing'
      },
      ja: {
        countries: 'src/app/ja/countries',
        articles: 'src/app/ja/articles',
        marketing: 'src/app/ja/marketing'
      }
    };

    this.templates = {};
    this.loadTemplates();
  }

  /**
   * 加载模板文件
   */
  loadTemplates() {
    try {
      this.templates = {
        default: fs.readFileSync('./templates/article-detail-template.tsx', 'utf8'),
        flag: fs.readFileSync('./templates/article-detail-flag-template.tsx', 'utf8'),
        nobanner: fs.readFileSync('./templates/article-detail-nobanner-template.tsx', 'utf8')
      };
      console.log('✅ Templates loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load templates:', error.message);
      throw error;
    }
  }

  /**
   * 确保目录存在
   * @param {string} dirPath 
   */
  ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dirPath}`);
    }
  }

  /**
   * 初始化目录结构
   */
  initializeDirectories() {
    console.log('🏗️ Initializing directory structure...');
    Object.values(this.outputDirs).forEach(langDirs => {
      Object.values(langDirs).forEach(dir => {
        this.ensureDirectoryExists(dir);
      });
    });

    // 确保数据目录存在
    const dataDirs = [
      'src/data/articles/countries',
      'src/data/articles/articles',
      'src/data/articles/marketing'
    ];
    dataDirs.forEach(dir => this.ensureDirectoryExists(dir));
  }

  /**
   * 安全的内容清理
   * @param {string} content 
   * @returns {string}
   */
  sanitizeContent(content) {
    if (!content) return '';
    
    return content
      .replace(/\r\n/g, '\\n')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\n')
      .replace(/'/g, "\\'")
      .replace(/"/g, '\\"')
      .replace(/`/g, '\\`')
      .trim();
  }

  /**
   * 安全的模板替换
   * @param {string} template 
   * @param {Object} data 
   * @returns {string}
   */
  replaceTemplate(template, data) {
    const replacements = {
      '${TITLE}': this.escapeForTemplate(data.title || ''),
      '${DESCRIPTION}': this.escapeForTemplate(data.description || ''),
      '${SITE_NAME}': this.escapeForTemplate(data.siteName || ''),
      '${FEATURE_IMAGE_URL}': data.featureImageUrl || '',
      '${COUNTRY_FLAG_IMAGE_URL}': data.countryFlagImageUrl || '',
      '${CONTENT}': this.sanitizeContent(data.content || ''),
      '${LANGUAGE}': data.language || '',
      '${LANGUAGE_SIMPLE}': data.languageSimple || '',
      '${FORM_CONFIRM_PROMPT}': this.escapeForTemplate(data.confirmPrompt || '')
    };

    let result = template;
    Object.entries(replacements).forEach(([placeholder, value]) => {
      result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
    });

    return result;
  }

  /**
   * 转义模板字符串
   * @param {string} str 
   * @returns {string}
   */
  escapeForTemplate(str) {
    if (!str) return '';
    return str.replace(/'/g, "\\'").replace(/"/g, '\\"');
  }

  /**
   * 带重试的GraphQL请求
   * @param {string} query 
   * @param {number} retries 
   * @returns {Promise<Object>}
   */
  async requestWithRetry(query, retries = CONFIG.maxRetries) {
    const client = new GraphQLClient(CONFIG.endpoint, {
      timeout: 30000,
      headers: {
        'User-Agent': 'SmartDeer-Article-Generator/1.0'
      }
    });

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 Fetching articles (attempt ${attempt}/${retries})...`);
        const data = await client.request(query);
        console.log(`✅ Successfully fetched ${data.posts.nodes.length} articles`);
        return data;
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error.message);
        
        if (attempt === retries) {
          throw new Error(`Failed to fetch articles after ${retries} attempts: ${error.message}`);
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay * attempt));
      }
    }
  }

  /**
   * 获取文章数据
   * @param {string} afterCursor 
   * @returns {Promise<void>}
   */
  async fetchArticles(afterCursor = '') {
    try {
      const query = this.buildGraphQLQuery(afterCursor, CONFIG.batchSize);
      const data = await this.requestWithRetry(query);
      
      // 处理文章
      for (const post of data.posts.nodes) {
        await this.generateArticleDetail(post);
      }

      // 检查是否有更多页面
      if (data.posts.pageInfo.hasNextPage) {
        await this.fetchArticles(data.posts.pageInfo.endCursor);
      } else {
        await this.generateArticleListFiles();
        console.log('🎉 Article generation completed successfully!');
      }
    } catch (error) {
      console.error('💥 Fatal error during article generation:', error.message);
      throw error;
    }
  }

  /**
   * 生成文章详情页
   * @param {ArticleData} post 
   */
  async generateArticleDetail(post) {
    try {
      if (!post.countryGuideExternal?.fileName) {
        console.warn(`⚠️ Skipping post ${post.id}: missing fileName`);
        return;
      }

      const articleListItemData = {
        id: post.id,
        title: post.title,
        image: post.countryGuideExternal.listingImage?.node?.sourceUrl || '',
        countryName: post.countryGuideExternal.countryName || ''
      };

      for (const category of post.categories.nodes) {
        await this.processCategory(post, category, articleListItemData);
      }
    } catch (error) {
      console.error(`❌ Error generating article ${post.id}:`, error.message);
    }
  }

  /**
   * 处理分类
   * @param {ArticleData} post 
   * @param {Object} category 
   * @param {Object} articleListItemData 
   */
  async processCategory(post, category, articleListItemData) {
    const categoryConfig = this.getCategoryConfig(category.categoryId);
    if (!categoryConfig) return;

    const template = this.getTemplate(category.categoryId);
    
    for (const langConfig of categoryConfig.languages) {
      const templateData = {
        title: post.title,
        description: post.seo?.opengraphDescription || '',
        siteName: post.seo?.opengraphSiteName || '',
        featureImageUrl: post.featuredImage?.node?.sourceUrl || '',
        countryFlagImageUrl: post.countryGuideExternal?.countryFlagImage?.node?.sourceUrl || '',
        content: post.content || '',
        language: langConfig.locale,
        languageSimple: langConfig.lang,
        confirmPrompt: langConfig.confirmPrompt
      };

      const content = this.replaceTemplate(template, templateData);
      await this.writeArticleFile(langConfig.lang, categoryConfig.type, post.countryGuideExternal.fileName, content, articleListItemData);
    }
  }

  /**
   * 获取分类配置
   * @param {number} categoryId 
   * @returns {Object|null}
   */
  getCategoryConfig(categoryId) {
    const configs = {
      4: { type: 'countries', languages: [{ lang: 'zh', locale: 'zh-CN', confirmPrompt: '您的请求已收到，我们会尽快与您联系。' }] },
      5: { type: 'countries', languages: [{ lang: 'en', locale: 'en-US', confirmPrompt: 'We have received your request and we will contact with you as soon as possible.' }] },
      7: { type: 'articles', languages: [{ lang: 'en', locale: 'en-US', confirmPrompt: 'We have received your request and we will contact with you as soon as possible.' }] },
      8: { type: 'articles', languages: [{ lang: 'zh', locale: 'zh-CN', confirmPrompt: '您的请求已收到，我们会尽快与您联系。' }] },
      10: { type: 'marketing', languages: [{ lang: 'en', locale: 'en-US', confirmPrompt: 'We have received your request and we will contact with you as soon as possible.' }] },
      11: { type: 'marketing', languages: [{ lang: 'zh', locale: 'zh-CN', confirmPrompt: '您的请求已收到，我们会尽快与您联系。' }] }
    };

    return configs[categoryId] || null;
  }

  /**
   * 获取模板
   * @param {number} categoryId 
   * @returns {string}
   */
  getTemplate(categoryId) {
    if (categoryId === 4 || categoryId === 5) {
      return this.templates.flag;
    } else if (categoryId === 7 || categoryId === 8) {
      return this.templates.nobanner;
    }
    return this.templates.default;
  }

  /**
   * 写入文章文件
   * @param {string} lang 
   * @param {string} type 
   * @param {string} fileName 
   * @param {string} content 
   * @param {Object} listItemData 
   */
  async writeArticleFile(lang, type, fileName, content, listItemData) {
    try {
      const outputDir = this.outputDirs[lang][type];
      const articleDir = path.join(outputDir, fileName);
      
      this.ensureDirectoryExists(articleDir);
      
      const pagePath = path.join(articleDir, 'page.tsx');
      fs.writeFileSync(pagePath, content, 'utf8');
      
      // 添加到列表
      const listKey = type === 'countries' ? 'guide' : type;
      this.articleLists[lang][listKey].push({
        ...listItemData,
        link: `/${lang}/${type}/${fileName}`
      });
      
      console.log(`✅ Generated: ${pagePath}`);
    } catch (error) {
      console.error(`❌ Error writing file for ${fileName}:`, error.message);
    }
  }

  /**
   * 生成文章列表文件
   */
  async generateArticleListFiles() {
    console.log('📝 Generating article list files...');
    
    const listConfigs = [
      { data: this.articleLists.zh.guide, path: 'src/data/articles/countries/article-list-zh.json' },
      { data: this.articleLists.en.guide, path: 'src/data/articles/countries/article-list-en.json' },
      { data: this.articleLists.ja.guide, path: 'src/data/articles/countries/article-list-ja.json' },
      { data: this.articleLists.zh.articles, path: 'src/data/articles/articles/article-list-zh.json' },
      { data: this.articleLists.en.articles, path: 'src/data/articles/articles/article-list-en.json' },
      { data: this.articleLists.ja.articles, path: 'src/data/articles/articles/article-list-ja.json' },
      { data: this.articleLists.zh.marketing, path: 'src/data/articles/marketing/article-list-zh.json' },
      { data: this.articleLists.en.marketing, path: 'src/data/articles/marketing/article-list-en.json' },
      { data: this.articleLists.ja.marketing, path: 'src/data/articles/marketing/article-list-ja.json' }
    ];

    for (const { data, path: filePath } of listConfigs) {
      try {
        this.ensureDirectoryExists(path.dirname(filePath));
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`✅ Generated list: ${filePath} (${data.length} articles)`);
      } catch (error) {
        console.error(`❌ Error generating list ${filePath}:`, error.message);
      }
    }
  }

  /**
   * 构建GraphQL查询
   * @param {string} after 
   * @param {number} limit 
   * @returns {string}
   */
  buildGraphQLQuery(after, limit) {
    const conditions = [`first: ${limit}`];
    
    if (after) {
      conditions.push(`after: "${after}"`);
    }
    
    conditions.push(`where: {orderby: {field: DATE, order: DESC}}`);
    
    return `
      query articleQuery {
        posts(${conditions.join(', ')}) {
          nodes {
            id
            title
            slug
            categories {
              nodes {
                categoryId
              }
            }
            seo {
              metaDesc
              metaKeywords
              opengraphDescription
              opengraphSiteName
              opengraphType
              title
            }
            featuredImage {
              node {
                sourceUrl(size: LARGE)
              }
            }
            countryGuideExternal {
              listingImage {
                node {
                  sourceUrl(size: LARGE)
                }
              }
              countryFlagImage {
                node {
                  sourceUrl(size: LARGE)
                }
              }
              countryName
              fileName
            }
            content
          }
          pageInfo {
            startCursor
            hasPreviousPage
            hasNextPage
            endCursor
          }
        }
      }
    `;
  }

  /**
   * 主执行函数
   */
  async run() {
    try {
      console.log('🚀 Starting Next.js article generation...');
      console.log(`📊 Configuration: endpoint=${CONFIG.endpoint}, batchSize=${CONFIG.batchSize}`);
      
      this.initializeDirectories();
      await this.fetchArticles();
      
      console.log('🎉 Article generation completed successfully!');
    } catch (error) {
      console.error('💥 Article generation failed:', error.message);
      process.exit(1);
    }
  }
}

// 主执行函数
async function main() {
  const generator = new ArticleGenerator();
  await generator.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  ArticleGenerator,
  main
};
