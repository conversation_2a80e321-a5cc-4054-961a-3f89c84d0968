/* --- <PERSON><PERSON> ALL the SCSS from your original Vue file here --- */
/* Then, make the following adjustments: */

/* 1. Remove the @import for 'en.scss', as those should be global. */

/* 2. Update scroll animation triggers to use data-show attribute */
/* For example, for the service section: */
.service .serviceList .serviceItem[data-show="true"] {
  &:nth-child(2n+1) {
    .figureArea, .serviceContent {
      right: 0px;
      left: 0px;
      opacity: 1;
    }
  }
  &:nth-child(2n) {
     .figureArea, .serviceContent {
      left: 0px;
      right: 0px;
      opacity: 1;
    }
  }
}

/* Do the same for other animated sections like advantage, lifecycle, etc. */
.advantages .advantageList[data-show="true"] .advantageItem {
  transform: scale(1);
}

.lifecycle .lifecycleList[data-show="true"] .lifecycleItem {
    left: 0;
    opacity: 1;
}

.process .processList .processItem[data-show="true"] .figureArea {
    top: 0;
    opacity: 1;
}

.solution .solutionList .solutionItem[data-show="true"] {
     .solution-figure, .solution-content {
        opacity: 1;
        left: 0;
        right: 0;
     }
}


/* 3. Add styles for the new modal component */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 600px;
  max-width: 90%;
  position: relative;
}

.modalCloseButton {
  position: absolute;
  top: 10px;
  right: 15px;
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

/* 4. Ensure all other styles from the original file are present */
@keyframes aniRotate {
  // ...
}

.indexPage {
  // ...
}

// etc.