'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '@/types';
import HomePageClientPC from './HomePageClientPC';
import HomePageClientMobile from './HomePageClientMobile';

interface HomePageClientProps {
  locale: Locale;
}

// 响应式检测Hook
function useResponsive() {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 确保在客户端环境中运行
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    // 检测设备类型的函数
    const checkDevice = () => {
      // 方法1: 使用window.innerWidth检测
      const width = window.innerWidth;
      
      // 方法2: 使用User Agent检测移动设备
      const userAgent = navigator.userAgent.toLowerCase();
      const mobileKeywords = [
        'mobile', 'android', 'iphone', 'ipad', 'ipod', 
        'blackberry', 'windows phone', 'opera mini'
      ];
      const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));
      
      // 方法3: 使用CSS媒体查询检测
      const mediaQuery = window.matchMedia('(max-width: 768px)');
      const isMobileMedia = mediaQuery.matches;

      // 方法4: 检测触摸设备
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      // 综合判断：优先考虑屏幕宽度，然后考虑User Agent、媒体查询和触摸设备
      const mobile = width <= 768 || isMobileMedia || (isMobileUA && isTouchDevice);
      
      setIsMobile(mobile);
      setIsLoading(false);
    };

    // 初始检测
    checkDevice();

    // 监听窗口大小变化
    const handleResize = () => {
      checkDevice();
    };

    // 监听方向变化（移动设备）
    const handleOrientationChange = () => {
      // 延迟检测，等待方向变化完成
      setTimeout(checkDevice, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return { isMobile, isLoading };
}

// 加载组件
function LoadingComponent() {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

// 主要的响应式组件
export default function HomePageClient({ locale }: HomePageClientProps) {
  const { isMobile, isLoading } = useResponsive();

  // 如果还在检测设备类型，显示加载状态
  if (isLoading) {
    return <LoadingComponent />;
  }

  // 根据设备类型渲染对应的组件
  return (
    <div className="responsive-container">
      {/* 开发环境下显示设备类型信息 */}
      {process.env.NODE_ENV === 'development' && typeof window !== 'undefined' && (
        <div className="fixed top-0 left-0 z-[9999] bg-black text-white px-2 py-1 text-xs">
          {isMobile ? 'Mobile' : 'Desktop'} | {window.innerWidth}px
        </div>
      )}

      {isMobile ? (
        <div key="mobile" className="animate-fade-in">
          <HomePageClientMobile locale={locale} />
        </div>
      ) : (
        <div key="desktop" className="animate-fade-in">
          <HomePageClientPC locale={locale} />
        </div>
      )}

      <style jsx>{`
        .responsive-container {
          width: 100%;
          min-height: 100vh;
        }

        .animate-fade-in {
          opacity: 1;
          animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        /* 确保移动端和PC端的基础样式 */
        @media (max-width: 768px) {
          .responsive-container {
            overflow-x: hidden;
          }
        }

        @media (min-width: 769px) {
          .responsive-container {
            min-width: 1280px;
          }
        }
      `}</style>
    </div>
  );
}

// 导出设备检测Hook供其他组件使用
export { useResponsive };
