'use client';

import React, { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Locale } from '@/types';
import { getContent, ServiceItem, AdvantageItem, LifecycleItem, ProcessItem, SolutionCase } from '@/content';
import { getQueryString } from '@/utils';
import CustomerList from '@/components/CustomerList';
import ContactForm from '@/components/ContactForm';
import { useSimpleChatBot } from '@/hooks/useChatBot';
import {
  useInViewAnimation,
  serviceTextAnimation,
  serviceImageAnimation,
  processTextAnimation,
  processImageAnimation,
  staggerItem
} from '@/hooks/useFramerMotionAnimation';
import '@/styles/v1-compat.css';
import '@/styles/components/homepage.css';

import { ScrollToTop } from '@/components/InteractiveElements';

interface HomePageClientPCProps {
  locale: Locale;
}

// PC端组件

// PC端滚动显示Hook
function useScrollShowPC(delayOffset = 0) {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delayOffset);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(element);
    return () => observer.unobserve(element);
  }, [delayOffset]);

  return { ref, isVisible };
}

// ServiceItemComponent 组件 - PC端版本
function ServiceItemComponent({ service, index, onShowContactForm }: { service: ServiceItem; index: number; onShowContactForm: () => void }) {
  const { ref: textRef, isInView: textInView } = useInViewAnimation({ threshold: 0.2 });
  const { ref: imageRef, isInView: imageInView } = useInViewAnimation({ threshold: 0.2 });
  const isEven = index % 2 === 1; // 偶数项图片在左，奇数项图片在右

  return (
    <div
      key={service.id}
      id={service.id}
      className="service-item"
    >
      {/* 内容区域 */}
      <motion.div
        ref={textRef}
        variants={serviceTextAnimation}
        initial="hidden"
        animate={textInView ? "visible" : "hidden"}
        className={`service-content relative w-[540px] flex-shrink-0 px-6 box-border ${
          isEven ? 'order-2' : 'order-1'
        }`}
      >
        <div className="service-title">
          <h3>
            {service.title}
          </h3>
        </div>
        <div className="service-desc">
          {service.description.map((paragraph, idx) => (
            <p key={idx}>
              {paragraph}
            </p>
          ))}
        </div>
        <div className="service-contact mt-8">
          <button
            className="service-contact-button"
            onClick={onShowContactForm}
          >
            Request More Information
            <svg className="inline-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </motion.div>

      {/* 图片/视频区域 */}
      <motion.div
        ref={imageRef}
        variants={serviceImageAnimation}
        initial="hidden"
        animate={imageInView ? "visible" : "hidden"}
        className={`figure-area relative min-h-[333px] ${
          isEven ? 'order-1' : 'order-2'
        }`}
      >
        <figure className="rounded-lg overflow-hidden shadow-lg">
          {service.isVideo ? (
            <video
              id="video-player"
              className="video-js w-[580px] h-[333px] bg-transparent "
              controls
              preload="auto"
            >
              <source src={service.image} type="video/mp4" />
            </video>
          ) : (
            <Image
              src={service.image}
              alt={service.alt}
              width={580}
              height={333}
              className="w-[580px] h-[333px] "
            />
          )}
        </figure>
      </motion.div>
    </div>
  );
}

// AdvantageItemComponent 组件 - PC端版本
function AdvantageItemComponent({ advantage, index }: { advantage: AdvantageItem; index: number }) {
  const { ref, isVisible } = useScrollShowPC(100);
  
  return (
    <motion.div
      key={index}
      ref={ref}
      variants={staggerItem}
      className={`advantage-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
      style={{ transitionDelay: advantage.delay }}
    >
      <figure className="advantage-icon-area">
        <Image
          src={advantage.icon}
          alt={advantage.alt}
          width={64}
          height={64}
          className="w-16 h-16"
        />
      </figure>
      <div className="advantage-title">
        {advantage.title}
      </div>
      <div className="advantage-content">
        {advantage.description}
      </div>
    </motion.div>
  );
}

// LifecycleItemComponent 组件 - PC端版本
function LifecycleItemComponent({ item, index }: { item: LifecycleItem; index: number }) {
  const { ref, isVisible } = useScrollShowPC(100);
  
  return (
    <motion.div
      key={index}
      ref={ref}
      variants={staggerItem}
      className={`lifecycle-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
      style={{ 
        ...getLifecycleStyle(item.style),
        transitionDelay: item.delay 
      }}
    >
      <div className="lifecycle-item-icon ">
        {item.hasArrow && (
          <div
            className="arrow "
            style={{ backgroundImage: 'url("/images/index/arrow.svg")' }}
          />
        )}
        <figure className="">
          <Image
            src={item.icon}
            alt={item.title}
            width={152}
            height={152}
          />
        </figure>
      </div>
      <div className="lifecycle-item-title text-center">
        <h3>
          {item.title}
        </h3>
      </div>
    </motion.div>
  );
}

// 获取生命周期样式
function getLifecycleStyle(styleStr: string) {
  if (styleStr.includes('top:20px')) return { top: '20px' };
  if (styleStr.includes('top:40px')) return { top: '40px' };
  if (styleStr.includes('top:60px')) return { top: '60px' };
  return { top: '0px' };
}

// PC端英文版本主组件
export default function HomePageClientPC({ locale }: HomePageClientPCProps) {
  const [showContactForm, setShowContactForm] = useState(false);
  const [showConsultantCode, setShowConsultantCode] = useState(false);
  const [solutionExpandStatus, setSolutionExpandStatus] = useState<Record<string, boolean>>({
    s1: false,
    s2: false,
    s3: false,
    s4: false,
    s5: false,
    s6: false
  });

  // 用于记录展开前的滚动位置
  const beforeExpandTopRef = useRef<number | null>(null);

  // 集成聊天机器人
  const { toggleChatBot } = useSimpleChatBot('7439335660751716386');

  // 获取当前语言的内容
  const content = getContent(locale);

  // 处理滚动到指定位置的逻辑
  useEffect(() => {
    const scrollTarget = getQueryString('scroll');
    if (scrollTarget) {
      setTimeout(() => {
        scrollToElement(`#${scrollTarget}`);
      }, 100);
    }
  }, []);

  const toggleSolution = (key: string) => {
    const isCurrentlyExpanded = solutionExpandStatus[key];

    if (!isCurrentlyExpanded) {
      // 展开前记录当前滚动位置
      beforeExpandTopRef.current = window.scrollY;
    } else {
      // 收起时平滑滚动回原位置
      if (beforeExpandTopRef.current !== null) {
        smoothScrollTo(700, beforeExpandTopRef.current);
        beforeExpandTopRef.current = null;
      }
    }

    setSolutionExpandStatus(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // 优化：移除了getSolutionDescClass，直接在组件中计算CSS类名

  const smoothScrollTo = (duration: number, target: number) => {
    const start = window.scrollY;
    const startTime = performance.now();

    function scrollStep(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const newScrollY = start + (target - start) * progress;
      window.scrollTo(0, newScrollY);
      if (progress < 1) {
        requestAnimationFrame(scrollStep);
      }
    }
    requestAnimationFrame(scrollStep);
  };

  const toggleChat = () => {
    toggleChatBot();
  };

  const submitSuccess = () => {
    setShowContactForm(false);
    // 这里可以添加成功提示
  };

  // 滚动到元素
  const scrollToElement = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

// ProcessItemComponent 组件 - PC端版本
function ProcessItemComponent({ item, index }: { item: ProcessItem; index: number }) {
  const { ref: textRef, isInView: textInView } = useInViewAnimation({ threshold: 0.2 });
  const { ref: imageRef, isInView: imageInView } = useInViewAnimation({ threshold: 0.2 });
  const { ref, isVisible } = useScrollShowPC(180);

  return (
    <div
      key={index}
      ref={ref}
      className={`process-item  ${index === 6 ? 'mb-0' : ''} ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      {/* 背景 */}
      <div className={`process-background  ${
        item.layout === 'right' ? 'layout-right' : 'layout-left'
      }`}>
        <div className={`process-num  ${
          item.layout === 'right' ? 'left-[50px]' : 'right-[50px]'
        }`}>
          {item.num}
        </div>
      </div>

      {/* 内容包装器 */}
      <div className={`process-content-wrapper`}>
        {/* 内容区域 */}
        <motion.div
          ref={textRef}
          variants={processTextAnimation}
          initial="hidden"
          animate={textInView ? "visible" : "hidden"}
          className={`process-content  ${
            item.layout === 'left' ? 'order-2' : 'order-1'
          }`}
        >
          <h3 className="text-[32px] font-semibold leading-[52px] text-gray-900 mb-6">
            {item.title}
          </h3>
          <p className="text-lg text-gray-700 leading-8">
            {item.description}
          </p>
        </motion.div>

        {/* 图片区域 */}
        <motion.div
          ref={imageRef}
          variants={processImageAnimation}
          initial="hidden"
          animate={imageInView ? "visible" : "hidden"}
          className={`figure-area ${
            item.layout === 'left' ? 'order-1' : 'order-2'
          }`}
        >
          {/* <figure className={`rounded-lg overflow-hidden shadow-lg`}>
            <Image
              src={`/api/trim-image?filename=${item.image}`}
              alt={item.alt}
              width={100}
              height={100}
              sizes="(max-width: 768px) 320px, 659px"
              // className="bg-white"
              quality={100}
            />
          </figure> */}
          <figure
            className={`
              rounded-lg 
              overflow-hidden 
              shadow-lg 
              w-[659px] 
              h-[400px] 
              bg-center 
              bg-cover 
              bg-no-repeat
            `}
            style={{
              backgroundImage: `url(/api/trim-image?filename=${item.image})`,
            }}
            aria-label={item.alt}
          >
          </figure>
        </motion.div>
      </div>
    </div>
  );
}

// SolutionItemComponent 组件 - PC端版本（优化性能）
const SolutionItemComponent = React.memo(function SolutionItemComponent({
  caseItem,
  isExpanded,
  onToggle,
  expandText,
  collapseText,
  index
}: {
  caseItem: SolutionCase;
  isExpanded: boolean;
  onToggle: () => void;
  expandText: string;
  collapseText: string;
  index: number
}) {
  const { ref, isVisible } = useScrollShowPC(180);

  return (
    <div
      key={caseItem.id}
      ref={ref}
      className={`solution-item ${caseItem.bgClass} ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <div className="solution-wrapper">
        {
          index % 2 === 0 ? (
            <>
            <div className="solution-figure">
              <figure>
                <Image
                  src={caseItem.image}
                  alt={caseItem.title}
                  width={400}
                  height={300}
                  className=""
                />
              </figure>
            </div>
            <div className="solution-content">
              <div className="solution-title">
                <h3>
                  {caseItem.title}
                </h3>
              </div>
              <div className={`solution-desc ${isExpanded ? 'expanded' : ''}`}>
                <div>
                  <div>
                    <h4>Background & Challenges</h4>
                    <p>{caseItem.background}</p>
                    <ol>
                      {caseItem.challenges.map((challenge: string, idx: number) => (
                        <li key={idx}>{challenge}</li>
                      ))}
                    </ol>
                    {caseItem.keyProblems && (
                      <div>
                        <h4>Key challenges faced include:</h4>
                        <ul>
                          {caseItem.keyProblems.map((problem: string, idx: number) => (
                            <li key={idx}>{problem}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4>Solutions Provided by SmartDeer</h4>
                    <p>To address the company&apos;s global needs and challenges, SmartDeer developed a comprehensive solution:</p>
                    <ol>
                      {caseItem.solutions.map((solution, idx) => (
                        <li key={idx}>
                          <strong>{solution.title}</strong>
                          <ul>
                            {solution.items.map((item: string, itemIdx: number) => (
                              <li key={itemIdx}>{item}</li>
                            ))}
                          </ul>
                        </li>
                      ))}
                    </ol>
                  </div>

                  <div>
                    <h4>Results Achieved</h4>
                    <ul>
                      {caseItem.results.map((result: string, idx: number) => (
                        <li key={idx}>{result}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4>Client Testimonial</h4>
                    <p>{caseItem.testimonial}</p>
                  </div>
                </div>
              </div>
              <div className="solution-expand">
                <button
                  onClick={onToggle}
                  className="solution-toggle"
                >
                  <span>{!isExpanded ? expandText : collapseText}</span>
                  <svg className="inline-arrow" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d={!isExpanded ? "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" : "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"} clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
            </>
          ) : (
            <>
              <div className="solution-content">
              <div className="solution-title">
                <h3>
                  {caseItem.title}
                </h3>
              </div>
              <div className={`solution-desc ${isExpanded ? 'expanded' : ''}`}>
                <div>
                  <div>
                    <h4>Background & Challenges</h4>
                    <p>{caseItem.background}</p>
                    <ol>
                      {caseItem.challenges.map((challenge: string, idx: number) => (
                        <li key={idx}>{challenge}</li>
                      ))}
                    </ol>
                    {caseItem.keyProblems && (
                      <div>
                        <h4>Key challenges faced include:</h4>
                        <ul>
                          {caseItem.keyProblems.map((problem: string, idx: number) => (
                            <li key={idx}>{problem}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4>Solutions Provided by SmartDeer</h4>
                    <p>To address the company&apos;s global needs and challenges, SmartDeer developed a comprehensive solution:</p>
                    <ol>
                      {caseItem.solutions.map((solution, idx) => (
                        <li key={idx}>
                          <strong>{solution.title}</strong>
                          <ul>
                            {solution.items.map((item: string, itemIdx: number) => (
                              <li key={itemIdx}>{item}</li>
                            ))}
                          </ul>
                        </li>
                      ))}
                    </ol>
                  </div>

                  <div>
                    <h4>Results Achieved</h4>
                    <ul>
                      {caseItem.results.map((result: string, idx: number) => (
                        <li key={idx}>{result}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4>Client Testimonial</h4>
                    <p>{caseItem.testimonial}</p>
                  </div>
                </div>
              </div>
              <div className="solution-expand">
                <button
                  onClick={onToggle}
                  className="solution-toggle"
                >
                  <span>{!isExpanded ? expandText : collapseText}</span>
                  <svg className="inline-arrow" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d={!isExpanded ? "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" : "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"} clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              </div>
              <div className="solution-figure">
                <figure>
                  <Image
                    src={caseItem.image}
                    alt={caseItem.title}
                    width={400}
                    height={300}
                    className=""
                  />
                </figure>
              </div>
            </>
          )
        }
        
      </div>
    </div>
  );
});

  return (
    <div className="index-page">
      {/* Header Section */}
      <header className="header-banner">
        <div className="header-banner-content">
          <div className="header-banner-text  animate-fade-in-up">
            <h2 className={`slogon ${locale === 'zh' ? '!h-auto slogonZh' : ''}`}>
              {content.hero.slogan}
            </h2>
            <h1 className={`title ${locale === 'en' ? 'titleEN' : ''}`}>
              {content.hero.title}
            </h1>
            <div className="desc">
              <span>
                {content.hero.description}
              </span>
              <figure className="absolute -left-[56px] -top-[6px] w-[140px] h-[140px]">
                <Image
                  src="/images/index/global-desc.webp"
                  alt="Global Description"
                  width={140}
                  height={140}
                  className="object-contain"
                />
              </figure>
            </div>
          </div>
          <div className="hidden ml-[20px]"></div>
          <div className={`header-banner-image relative animate-fade-in-right min-h-[650px] flex-1 ml-[20px] 2xl:!ml-[173px]`}>
            <figure className="global-min_1 absolute top-1/2 left-1/2 w-[598px] h-[600px] -mt-[300px] -ml-[299px] z-10">
              <Image
                src="/images/index/global-min_1.png"
                alt="Global Service 1"
                width={598}
                height={600}
                className="w-full h-full transform rotate-45 animate-spin-slow"
              />
            </figure>
            <figure className="global-min_2 absolute top-1/2 left-1/2 w-[480px] h-[480px] -mt-[240px] -ml-[240px] z-20">
              <Image
                src="/images/index/global-min_2.png"
                alt="Global Service 2"
                width={480}
                height={480}
                className="w-full h-full animate-spin"
              />
            </figure>
            <figure className="global-min_3 absolute top-1/2 left-1/2 w-[598px] h-[600px] -mt-[300px] -ml-[299px] z-30">
              <Image
                src="/images/index/global-min_3.png"
                alt="Global Service 3"
                width={559}
                height={556}
                className="absolute top-[19px] -left-[10px]"
              />
            </figure>
          </div>
        </div>
      </header>

      <main>
        {/* Customer Section */}
        <section className="customer">
          <div className="max-w-7xl mx-auto px-6">
            <div className="section-title">
              <h2>
                {content.customer.title}
              </h2>
              <p>Serve Global Customers</p>
            </div>
            <CustomerList />
          </div>
        </section>

        {/* Services Section */}
        <section className="service overflow-hidden" >
          <div className="section-title text-center ">
            <h2>
              {content.services.title}
            </h2>
            <p>{content.services.subtitle}</p>
          </div>

          <div className="service-list ">
            {content.services.items.map((service: ServiceItem, index: number) => (
              <ServiceItemComponent
                key={service.id}
                service={service}
                index={index}
                onShowContactForm={() => setShowContactForm(true)}
              />
            ))}
          </div>
        </section>

        {/* Advantages Section */}
        <section className="advantages">
          <div className="w-[1200px] mx-auto px-4">
            <div className="section-title">
              <h2>
                {content.advantages.title}
              </h2>
              <p>{content.advantages.subtitle}</p>
            </div>

            <div className="advantage-list flex justify-between gap-6">
              {content.advantages.items.map((advantage: AdvantageItem, index: number) => (
                <AdvantageItemComponent key={index} advantage={advantage} index={index} />
              ))}
            </div>
          </div>
        </section>

        {/* Lifecycle Section */}
        <section className="lifecycle">
          <div className="w-full">
            <div className="section-title">
              <h2>
                {content.lifecycle.title}
              </h2>
              <p>{content.lifecycle.subtitle}</p>
            </div>

            <div className="lifecycle-list">
              {content.lifecycle.items.map((item: LifecycleItem, index: number) => (
                <LifecycleItemComponent key={index} item={item} index={index} />
              ))}
            </div>

            <div className="lifecycle-repeat">
              <Image
                src="/images/index/repeat.svg"
                alt="Lifecycle Repeat"
                width={800}
                height={100}
                className="w-full h-auto"
              />
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="process overflow-hidden">
          <div className="section-title text-center py-20">
            <h2>
              {content.process.title}
            </h2>
            <p>{content.process.subtitle}</p>
          </div>

          <div className="process-list w-full">
            {content.process.items.map((item, index: number) => (
              <ProcessItemComponent key={index} item={item as ProcessItem} index={index} />
            ))}
          </div>
        </section>

        {/* Solution Section */}
        <section id="solution" className="solution">
          <div className="">
            <div className="section-title">
              <h2>
                {content.solution.title}
              </h2>
              <p>{content.solution.subtitle}</p>
            </div>

            <div className="solution-list space-y-16">
              {content.solution.cases.map((caseItem: SolutionCase, index) => (
                <SolutionItemComponent
                  index={index}
                  key={caseItem.id}
                  caseItem={caseItem}
                  isExpanded={solutionExpandStatus[caseItem.id]}
                  onToggle={() => toggleSolution(caseItem.id)}
                  expandText={content.solution.expandText}
                  collapseText={content.solution.collapseText}
                />
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* Contact Form */}
      {showContactForm && (
        <ContactForm
          locale={locale}
          onClose={() => setShowContactForm(false)}
          onSubmit={submitSuccess}
        />
      )}

      {/* Floating Elements */}
      {/* Consultant */}
      <div className="anchor fixed bottom-32 right-8 z-50">
        <div
          className="consultant cursor-pointer"
          onClick={() => setShowConsultantCode(!showConsultantCode)}
        >
          <figure className="w-16 h-16">
            <Image
              src="/images/index/anchor-avatar-en.png"
              alt="Consultant"
              width={64}
              height={64}
              className="w-full h-full rounded-full shadow-lg"
            />
          </figure>
        </div>
        {showConsultantCode && (
          <div className="consultant-code absolute bottom-20 right-0 bg-white p-4 rounded-lg shadow-lg">
            <div
              className="close absolute top-2 right-2 cursor-pointer"
              onClick={() => setShowConsultantCode(false)}
            >
              ×
            </div>
            <figure className="w-32 h-32">
              <Image
                src="/images/index/anchor-code-en.png"
                alt="Consultant QR Code"
                width={128}
                height={128}
                className="w-full h-full"
              />
            </figure>
          </div>
        )}
      </div>

      {/* Bot Button */}
      <div
        className="bot-container fixed bottom-16 right-8 cursor-pointer z-50"
        onClick={toggleChat}
      >
        <Image
          src="/images/index/bot_logo_en.png"
          alt="Bot"
          width={60}
          height={60}
          className="w-15 h-15 rounded-full shadow-lg"
        />
      </div>

      {/* Go Top */}
      <div
        className="go-top-container fixed bottom-4 right-8 cursor-pointer z-50"
        onClick={() => smoothScrollTo(500, 0)}
      >
        <Image
          src="/images/index/top_icon.png"
          alt="Go Top"
          width={40}
          height={40}
          className="w-10 h-10 rounded-full shadow-lg"
        />
      </div>

      <ScrollToTop />
    </div>
  );
}
