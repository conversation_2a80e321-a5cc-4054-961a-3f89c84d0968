'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Locale } from '@/types';
import '@/styles/v1-compat.css';

// 添加v1移动端计算器的样式
const mobileCalculatorStyles = `
.contries-page header {
  background: #fff6ec;
}

.contries-page .header-banner {
  position: relative;
  width: 90vw;
  min-width: 0;
  height: auto;
  margin: 0 auto;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
  position: static;
}

.contries-page .header-banner-text {
  position: static;
  width: 100%;
  margin-top: 40px;
}

.contries-page .header-banner-text .header-title {
  font-size: 24px;
  margin-top: 0;
  text-align: center;
  font-weight: bold;
}

.contries-page .header-banner-text .header-desc {
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  font-weight: 300;
}

.contries-page .header-form {
  position: static;
  width: 100%;
  margin: 24px auto;
  padding: 24px 10px;
  box-shadow: none;
  border-radius: 16px;
  background: #fff;
  border: 1px #eff0f6 solid;
}

.contries-page .calculator-submit {
  color: #fff;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.contries-page .calculator-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.contries-page .form-item {
  margin-bottom: 16px;
}

.contries-page .form-select,
.contries-page .form-input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.contries-page .errorMessage {
  color: red;
  margin: 20px 0;
  font-size: 14px;
}

.contries-page .calculator-container {
  width: 90vw;
  min-width: 0;
  padding: 0 10px;
  box-sizing: border-box;
  margin: 0 auto;
}

.contries-page .calculator-container .result-title {
  font-size: 22px;
  margin-top: 50px;
  text-align: center;
}

.contries-page .calculator-container .result-description {
  font-size: 14px;
  text-align: center;
  color: #6f6c90;
  line-height: 20px;
}

.contries-page .result-table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 20px;
}

.contries-page .result-table tr,
.contries-page .result-table th,
.contries-page .result-table td {
  border: 1px #dbdee5 solid;
}

.contries-page .result-table td {
  width: 50%;
  font-size: 12px;
  padding: 12px 8px;
  color: #170f49;
  font-weight: 500;
}

.contries-page .cost-name-detail,
.contries-page .cost-amount-detail {
  background: rgba(239, 239, 239, 0.3);
}

.contries-page .cost-position {
  position: relative;
}

.contries-page .cost-position .icon {
  cursor: pointer;
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  right: 18px;
  top: 20px;
}

.contries-page .result-notice {
  color: #6f6c90;
  font-size: 12px;
  line-height: 16px;
  margin-top: 15px;
}

.contries-page .entity-notice {
  margin-top: 68px;
  font-size: 18px;
  font-weight: 400;
  color: #170f49;
}

.contries-page .calculator-contact {
  display: block;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border-radius: 20px;
  width: 100%;
  height: 40px;
  color: #fff;
  border: 0;
  background: linear-gradient(252deg, #f54a25 -45%, #ffab71 98%);
  box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
  margin: 20px auto 40px;
}

.contries-page .periodChange {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  margin-top: 20px;
}

.contries-page .el-dropdown-time {
  display: flex;
  gap: 10px;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}
`;

interface CalculatorClientMobileProps {
  locale: Locale;
}

interface FormData {
  country: string;
  province: string;
  city: string;
  currency: string;
  employType: string;
  annualSalary: number | null;
}

interface LocationOption {
  key: string;
  areaCode?: string;
  areaNameI18n: string;
  province?: string;
}

interface EmployTypeOption {
  key: string;
  sectionNameI18n: string;
}

interface CalculatorResult {
  grossSalary: number;
  netSalary: number;
  totalCosts: number;
  totalCostsMonthly: number;
  employerCosts: {
    socialSecurity: number;
    healthInsurance: number;
    unemploymentInsurance: number;
    otherBenefits: number;
  };
}

export default function CalculatorClientMobile({ locale }: CalculatorClientMobileProps) {
  const [formData, setFormData] = useState<FormData>({
    country: '',
    province: '',
    city: '',
    currency: '',
    employType: '',
    annualSalary: null
  });

  const [countryList, setCountryList] = useState<LocationOption[]>([]);
  const [provinceList, setProvinceList] = useState<LocationOption[]>([]);
  const [allCityList, setAllCityList] = useState<LocationOption[]>([]);
  const [cityList, setCityList] = useState<LocationOption[]>([]);
  const [employTypeList, setEmployTypeList] = useState<EmployTypeOption[]>([]);
  const [descriptionI18n, setDescriptionI18n] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  const [calculateResult, setCalculateResult] = useState<CalculatorResult | null>(null);
  const [period, setPeriod] = useState<'year' | 'month'>('year');
  const [showTableDetails, setShowTableDetails] = useState(false);

  const formRef = useRef<HTMLFormElement>(null);

  // 注入样式
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = mobileCalculatorStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // 获取内容配置 - 移动端版本
  const getContent = () => {
    const content = {
      zh: {
        title: '确定全球雇主成本',
        description: '准备好在另一个国家/地区雇用员工了吗？根据团队成员位置即时汇总雇佣成本，为您的企业做出最佳的招聘举措。',
        calculatorTitle: '雇佣成本计算器',
        calculatorDesc: '选择您要雇佣员工的国家和地区，填入员工年度薪资，即可查询在当地的雇主成本。',
        placeholders: {
          country: '国家/地区',
          province: '省份/州',
          city: '城市',
          employType: '雇佣类型',
          salary: '年薪'
        },
        submitButton: '获取雇主成本',
        resultTitle: '确定全球雇主成本',
        resultDesc: 'SmartDeer 可以帮助您决定雇佣下一位员工的最佳地点，并全面了解您可以预期支付的税费、费用、福利等。',
        tableHeaders: {
          item: '项目',
          annual: '年度',
          monthly: '月度'
        },
        tableItems: {
          grossSalary: '员工总薪资',
          netSalary: '员工净薪资',
          totalCosts: '雇主总成本'
        },
        notice: '计算结果是根据一个国家/地区的当地税收和合规成本得出的估计数字，净付款和雇主缴款可能会根据员工的个人数据而变化。',
        entityNotice: 'SmartDeer 在全球拥有实体。这意味着您可以在所有这些国家/地区雇佣员工，而无需开设自己的公司实体。',
        contactButton: '联系我们'
      },
      en: {
        title: 'Determine employee costs across the globe',
        description: 'Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.',
        calculatorTitle: 'Employee Cost Calculator',
        calculatorDesc: 'Select the country/region you wish to hire, and enter the employee\'s annual salary to check the employer cost in that location.',
        placeholders: {
          country: 'Country/Region',
          province: 'Province/State',
          city: 'City',
          employType: 'Employment Type',
          salary: 'Annual Salary'
        },
        submitButton: 'Get Employer Cost',
        resultTitle: 'Determine employee costs across the globe',
        resultDesc: 'SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.',
        tableHeaders: {
          item: 'Item',
          annual: 'Annual',
          monthly: 'Monthly'
        },
        tableItems: {
          grossSalary: 'Employee Gross Salary',
          netSalary: 'Employee Net Salary',
          totalCosts: 'Total Employer Cost'
        },
        notice: 'The calculation results are estimates based on local taxes and compliance costs in a country/region. Net payments and employer contributions may vary based on individual employee data.',
        entityNotice: 'SmartDeer has entities globally. This means you can hire employees in all these countries/regions without setting up your own company entity.',
        contactButton: 'Contact Us'
      },
      jp: {
        title: '世界各国の雇用コストを算出',
        description: '他国での雇用をお考えですか？チームメンバーの所在地に基づいて雇用コストを即座に集計し、ビジネスに最適な雇用戦略を立てましょう。',
        calculatorTitle: '雇用コスト計算機',
        calculatorDesc: '雇用したい国・地域を選択し、従業員の年収を入力すると、その地域での雇用主コストを確認できます。',
        placeholders: {
          country: '国・地域',
          province: '州・省',
          city: '都市',
          employType: '雇用形態',
          salary: '年収'
        },
        submitButton: '雇用主コストを取得',
        resultTitle: '世界各国の雇用コストを算出',
        resultDesc: 'SmartDeerは、次の従業員を雇用する最適な場所を決定し、支払うことが予想される税金、手数料、福利厚生などを包括的に理解するお手伝いをします。',
        tableHeaders: {
          item: '項目',
          annual: '年間',
          monthly: '月間'
        },
        tableItems: {
          grossSalary: '従業員総給与',
          netSalary: '従業員手取り給与',
          totalCosts: '雇用主総コスト'
        },
        notice: '計算結果は、国の現地税金・コンプライアンスコストを基にした推定値です。個々の従業員データによって、実際の支給額や企業負担額が変動する場合があります。',
        entityNotice: 'SmartDeerは世界中に現地法人を保有しており、現地法人を設立することなく、各国で従業員を雇用できます。',
        contactButton: 'お問い合わせ'
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  // 格式化数字
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined) return '0';
    return new Intl.NumberFormat('en-US').format(num);
  };

  // 滚动到指定元素
  const scrollTo = (selector: string) => {
    const element = document.querySelector(selector);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // 模拟获取国家列表
  const fetchCountryList = async () => {
    // 这里应该调用实际的API
    const mockCountries: LocationOption[] = [
      { key: 'US', areaNameI18n: locale === 'zh' ? '美国' : locale === 'jp' ? 'アメリカ' : 'United States' },
      { key: 'CN', areaNameI18n: locale === 'zh' ? '中国' : locale === 'jp' ? '中国' : 'China' },
      { key: 'JP', areaNameI18n: locale === 'zh' ? '日本' : locale === 'jp' ? '日本' : 'Japan' },
      { key: 'UK', areaNameI18n: locale === 'zh' ? '英国' : locale === 'jp' ? 'イギリス' : 'United Kingdom' },
      { key: 'DE', areaNameI18n: locale === 'zh' ? '德国' : locale === 'jp' ? 'ドイツ' : 'Germany' },
      { key: 'FR', areaNameI18n: locale === 'zh' ? '法国' : locale === 'jp' ? 'フランス' : 'France' },
      { key: 'CA', areaNameI18n: locale === 'zh' ? '加拿大' : locale === 'jp' ? 'カナダ' : 'Canada' },
      { key: 'AU', areaNameI18n: locale === 'zh' ? '澳大利亚' : locale === 'jp' ? 'オーストラリア' : 'Australia' }
    ];
    setCountryList(mockCountries);
  };

  // 处理国家变化
  const handleCountryChange = async (countryCode: string) => {
    setFormData(prev => ({
      ...prev,
      country: countryCode,
      province: '',
      city: '',
      employType: '',
      currency: ''
    }));

    // 清空下级选项
    setProvinceList([]);
    setAllCityList([]);
    setCityList([]);
    setEmployTypeList([]);
    setErrorMessage('');

    // 获取该国家的配置信息
    await fetchCountryConfig(countryCode);
  };

  // 获取国家配置信息
  const fetchCountryConfig = async (countryCode: string) => {
    try {
      // 模拟API调用
      const mockConfig = getMockCountryConfig(countryCode);
      
      if (!mockConfig.enabled) {
        setErrorMessage(mockConfig.errorMessage || (locale === 'zh' ? '当前国家暂不支持成本计算' : 
                                                   locale === 'jp' ? '現在この国はコスト計算をサポートしていません' : 
                                                   'Cost calculation is not currently supported for this country'));
        return;
      }

      setProvinceList(mockConfig.provinces || []);
      setAllCityList(mockConfig.cities || []);
      setEmployTypeList(mockConfig.employTypes || []);
      setFormData(prev => ({
        ...prev,
        currency: mockConfig.currency || 'USD'
      }));
    } catch (error) {
      console.error('Failed to fetch country config:', error);
      setErrorMessage(locale === 'zh' ? '获取国家配置失败，请稍后重试' : 
                     locale === 'jp' ? '国の設定の取得に失敗しました。後でもう一度お試しください' : 
                     'Failed to fetch country configuration, please try again later');
    }
  };

  // 模拟国家配置数据
  const getMockCountryConfig = (countryCode: string) => {
    const configs: Record<string, any> = {
      'US': {
        enabled: true,
        currency: 'USD',
        provinces: [
          { key: 'CA', areaNameI18n: 'California' },
          { key: 'NY', areaNameI18n: 'New York' },
          { key: 'TX', areaNameI18n: 'Texas' }
        ],
        cities: [
          { key: 'SF', areaNameI18n: 'San Francisco', province: 'CA' },
          { key: 'LA', areaNameI18n: 'Los Angeles', province: 'CA' },
          { key: 'NYC', areaNameI18n: 'New York City', province: 'NY' }
        ],
        employTypes: [
          { key: 'FULL_TIME', sectionNameI18n: locale === 'zh' ? '全职' : locale === 'jp' ? 'フルタイム' : 'Full Time' },
          { key: 'CONTRACTOR', sectionNameI18n: locale === 'zh' ? '承包商' : locale === 'jp' ? '契約社員' : 'Contractor' }
        ]
      },
      'CN': {
        enabled: true,
        currency: 'CNY',
        provinces: [
          { key: 'BJ', areaNameI18n: locale === 'zh' ? '北京' : locale === 'jp' ? '北京' : 'Beijing' },
          { key: 'SH', areaNameI18n: locale === 'zh' ? '上海' : locale === 'jp' ? '上海' : 'Shanghai' },
          { key: 'GD', areaNameI18n: locale === 'zh' ? '广东' : locale === 'jp' ? '広東' : 'Guangdong' }
        ],
        cities: [
          { key: 'BJ_CITY', areaNameI18n: locale === 'zh' ? '北京市' : locale === 'jp' ? '北京市' : 'Beijing City', province: 'BJ' },
          { key: 'SH_CITY', areaNameI18n: locale === 'zh' ? '上海市' : locale === 'jp' ? '上海市' : 'Shanghai City', province: 'SH' },
          { key: 'SZ', areaNameI18n: locale === 'zh' ? '深圳' : locale === 'jp' ? '深圳' : 'Shenzhen', province: 'GD' }
        ],
        employTypes: [
          { key: 'FULL_TIME', sectionNameI18n: locale === 'zh' ? '全职' : locale === 'jp' ? 'フルタイム' : 'Full Time' },
          { key: 'CONTRACTOR', sectionNameI18n: locale === 'zh' ? '承包商' : locale === 'jp' ? '契約社員' : 'Contractor' }
        ]
      }
    };

    return configs[countryCode] || { enabled: false, errorMessage: 'Country not supported' };
  };

  // 处理表单变化
  const handleFormChange = () => {
    if (formData.province) {
      const filteredCities = allCityList.filter(city => city.province === formData.province);
      setCityList(filteredCities);
    }
  };

  // 处理省份变化
  const handleProvinceChange = (provinceCode: string) => {
    setFormData(prev => ({
      ...prev,
      province: provinceCode,
      city: ''
    }));
    
    const filteredCities = allCityList.filter(city => city.province === provinceCode);
    setCityList(filteredCities);
  };

  // 计算雇主成本
  const calculateEmployerCost = async () => {
    if (!formData.country || !formData.annualSalary) {
      return;
    }

    if (cityList.length > 0 && !formData.city) {
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 获取该国家的税率配置
      const baseRate = getMockTaxRates(formData.country);

      // 计算各项成本
      const grossSalary = formData.annualSalary;
      const employerCosts = {
        socialSecurity: grossSalary * baseRate.socialSecurity,
        healthInsurance: grossSalary * baseRate.healthInsurance,
        unemploymentInsurance: grossSalary * baseRate.unemploymentInsurance,
        otherBenefits: grossSalary * baseRate.otherBenefits
      };

      const totalEmployerCost = grossSalary + Object.values(employerCosts).reduce((sum, cost) => sum + cost, 0);
      const netSalary = grossSalary * (1 - baseRate.incomeTax);

      const result: CalculatorResult = {
        grossSalary,
        netSalary,
        totalCosts: totalEmployerCost,
        totalCostsMonthly: totalEmployerCost / 12,
        employerCosts
      };

      setCalculateResult(result);
      setShowTable(true);
      
      // 滚动到结果区域
      setTimeout(() => {
        scrollTo('#calculate-result');
      }, 100);

    } catch (error) {
      console.error('Failed to calculate employer cost:', error);
      setErrorMessage(locale === 'zh' ? '计算失败，请稍后重试' : 
                     locale === 'jp' ? '計算に失敗しました。後でもう一度お試しください' : 
                     'Calculation failed, please try again later');
    } finally {
      setIsLoading(false);
    }
  };

  // 模拟税率数据
  const getMockTaxRates = (countryCode: string) => {
    const rates: Record<string, any> = {
      'US': {
        socialSecurity: 0.062,
        healthInsurance: 0.0145,
        unemploymentInsurance: 0.006,
        otherBenefits: 0.03,
        incomeTax: 0.22
      },
      'CN': {
        socialSecurity: 0.16,
        healthInsurance: 0.02,
        unemploymentInsurance: 0.005,
        otherBenefits: 0.015,
        incomeTax: 0.20
      }
    };

    return rates[countryCode] || rates['US'];
  };

  // 处理联系我们
  const handleContactUs = () => {
    setShowContactForm(true);
  };

  // 初始化
  useEffect(() => {
    fetchCountryList();
  }, []);

  // 监听表单变化
  useEffect(() => {
    handleFormChange();
  }, [formData.province, allCityList]);

  return (
    <div className="contries-page">
      {/* Header */}
      <header>
        <div className="header-banner">
          <div className="header-banner-text">
            <h1 className="header-title">{content.title}</h1>
            <p className="header-desc">{content.description}</p>
          </div>
          <div className="header-form">
            <div className="form-body">
              <form ref={formRef}>
                {/* 国家选择 */}
                <div className="form-item">
                  <select
                    value={formData.country}
                    onChange={(e) => handleCountryChange(e.target.value)}
                    className="form-select"
                    style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    required
                  >
                    <option value="">{content.placeholders.country}</option>
                    {countryList.map((country) => (
                      <option key={country.areaCode || country.key} value={country.areaCode || country.key}>
                        {country.areaNameI18n}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 省份选择 */}
                {provinceList.length > 0 && (
                  <div className="form-item">
                    <select
                      value={formData.province}
                      onChange={(e) => handleProvinceChange(e.target.value)}
                      className="form-select"
                      style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    >
                      <option value="">{content.placeholders.province}</option>
                      {provinceList.map((province) => (
                        <option key={province.key} value={province.key}>
                          {province.areaNameI18n}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* 城市选择 */}
                {cityList.length > 0 && (
                  <div className="form-item">
                    <select
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      className="form-select"
                      style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    >
                      <option value="">{content.placeholders.city}</option>
                      {cityList.map((city) => (
                        <option key={city.key} value={city.key}>
                          {city.areaNameI18n}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* 雇佣类型选择 */}
                {employTypeList.length > 0 && (
                  <div className="form-item">
                    <select
                      value={formData.employType}
                      onChange={(e) => setFormData(prev => ({ ...prev, employType: e.target.value }))}
                      className="form-select"
                      style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    >
                      <option value="">{content.placeholders.employType}</option>
                      {employTypeList.map((type) => (
                        <option key={type.key} value={type.key}>
                          {type.sectionNameI18n}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* 货币显示 */}
                {formData.currency && (
                  <div className="form-item">
                    <input
                      type="text"
                      value={formData.currency}
                      disabled
                      style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    />
                  </div>
                )}

                {/* 年薪输入 */}
                <div className="form-item">
                  <input
                    type="number"
                    value={formData.annualSalary || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      annualSalary: e.target.value ? Number(e.target.value) : null
                    }))}
                    placeholder="员工年度薪资"
                    style={{ width: '100%', height: '40px', fontSize: '14px' }}
                    min="0"
                  />
                </div>

                {/* 错误信息 */}
                {errorMessage && (
                  <div className="errorMessage" onClick={() => setShowContactForm(true)}>
                    {errorMessage}
                  </div>
                )}

                {/* 提交按钮 */}
                <button
                  type="button"
                  onClick={calculateEmployerCost}
                  disabled={
                    isLoading ||
                    errorMessage !== '' ||
                    !formData.country ||
                    !formData.annualSalary ||
                    (cityList.length > 0 && !formData.city)
                  }
                  className="calculator-submit"
                >
                  获取雇主成本
                </button>
              </form>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Results Section */}
      <div id="calculate-result" className="calculator-container mobile-results">
        <h2 className="result-title mobile-result-title">{content.resultTitle}</h2>
        <p className="result-description mobile-result-desc">
          {content.resultDesc}
        </p>

        {/* Period Toggle */}
        <div className="period-toggle mobile-period-toggle">
          <button
            className={`period-btn ${period === 'year' ? 'active' : ''}`}
            onClick={() => setPeriod('year')}
          >
            {content.tableHeaders.annual}
          </button>
          <button
            className={`period-btn ${period === 'month' ? 'active' : ''}`}
            onClick={() => setPeriod('month')}
          >
            {content.tableHeaders.monthly}
          </button>
        </div>

        {/* Results Table */}
        {showTable && calculateResult && (
          <table className="result-table">
            <tbody>
              <tr>
                <td className="cost-name">
                  {period === 'year' ? '年' : '月'}薪总额
                </td>
                <td className="cost-amount">
                  {formData.currency} {formatNumber(period === 'year' ? calculateResult.grossSalary : calculateResult.grossSalary / 12)}
                </td>
              </tr>
              <tr>
                <td className="cost-name cost-position">
                  <span className="icon" onClick={() => setShowTableDetails(!showTableDetails)}>
                    {showTableDetails ? '▲' : '▼'}
                  </span>
                  <span>{period === 'year' ? '年' : '月'}度总计雇佣成本</span>
                </td>
                <td className="cost-amount">
                  {formData.currency} {formatNumber(period === 'year' ? calculateResult.totalCosts : calculateResult.totalCostsMonthly)}
                </td>
              </tr>
              {showTableDetails && (
                <>
                  <tr>
                    <td className="cost-name-detail">社会保险</td>
                    <td className="cost-amount-detail">
                      {formData.currency} {formatNumber(period === 'year' ? calculateResult.employerCosts.socialSecurity : calculateResult.employerCosts.socialSecurity / 12)}
                    </td>
                  </tr>
                  <tr>
                    <td className="cost-name-detail">健康保险</td>
                    <td className="cost-amount-detail">
                      {formData.currency} {formatNumber(period === 'year' ? calculateResult.employerCosts.healthInsurance : calculateResult.employerCosts.healthInsurance / 12)}
                    </td>
                  </tr>
                  <tr>
                    <td className="cost-name-detail">失业保险</td>
                    <td className="cost-amount-detail">
                      {formData.currency} {formatNumber(period === 'year' ? calculateResult.employerCosts.unemploymentInsurance : calculateResult.employerCosts.unemploymentInsurance / 12)}
                    </td>
                  </tr>
                  <tr>
                    <td className="cost-name-detail">其他福利</td>
                    <td className="cost-amount-detail">
                      {formData.currency} {formatNumber(period === 'year' ? calculateResult.employerCosts.otherBenefits : calculateResult.employerCosts.otherBenefits / 12)}
                    </td>
                  </tr>
                </>
              )}
            </tbody>
          </table>
        )}

        {showTable && (
          <>
            <p className="result-notice mobile-notice">
              {content.notice}
            </p>
            {descriptionI18n.map((item, index) => (
              <p key={index} className="result-notice mobile-notice">
                {item}
              </p>
            ))}
          </>
        )}

        <p className="entity-notice mobile-entity-notice">
          {content.entityNotice}
        </p>

        <button className="calculator-contact mobile-contact" onClick={handleContactUs}>
          {content.contactButton}
        </button>
      </div>

      {/* Mobile Contact Form Modal */}
      {showContactForm && (
        <div className="modal-overlay mobile-modal" onClick={() => setShowContactForm(false)}>
          <div className="modal-content mobile-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header mobile-modal-header">
              <button className="modal-close mobile-modal-close" onClick={() => setShowContactForm(false)}>
                ×
              </button>
            </div>
            <div className="modal-body mobile-modal-body">
              {/* 这里应该放置ContactForm组件 */}
              <p>Contact form will be implemented here</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
