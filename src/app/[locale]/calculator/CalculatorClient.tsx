'use client';

import React from 'react';
import { Locale } from '@/types';
import CalculatorClientPC from './CalculatorClientPC';
import CalculatorClientMobile from './CalculatorClientMobile';

import '@/styles/v1-compat.css';
import '@/styles/components/calculator.css';

interface CalculatorClientProps {
  locale: Locale;
}

/**
 * Calculator响应式入口组件 - 使用Tailwind CSS响应式断点控制PC和移动端组件显示
 *
 * 技术实现：
 * 1. 使用Tailwind CSS的响应式断点（md:block md:hidden）控制显示
 * 2. PC端组件在md断点（768px）以上显示
 * 3. 移动端组件在md断点以下显示
 * 4. 确保UI完全还原，包括视觉元素、交互行为和动画效果
 * 5. 保持组件功能性和数据流逻辑不变
 */
export default function CalculatorClient({ locale }: CalculatorClientProps) {
  return (
    <div className="responsive-calculator-container">
      {/* PC端组件 - 在md断点（768px）及以上显示 */}
      <div className="hidden md:block">
        <CalculatorClientPC locale={locale} />
      </div>

      {/* 移动端组件 - 在md断点（768px）以下显示 */}
      <div className="block md:hidden">
        <CalculatorClientMobile locale={locale} />
      </div>
    </div>
  );
}
