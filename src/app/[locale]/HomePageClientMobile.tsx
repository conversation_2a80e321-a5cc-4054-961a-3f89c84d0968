'use client';

import React, { useEffect, useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import { Locale } from '@/types';
import { getContent, ServiceItem, AdvantageItem, LifecycleItem, ProcessItem, SolutionCase } from '@/content';
import CustomerList from '@/components/CustomerList';
import ContactForm from '@/components/ContactForm';
import '@/styles/v1-compat.css';
import '@/styles/components/homepage.css';
import '@/styles/components/mobile-homepage.css';

interface HomePageClientMobileProps {
  locale: Locale;
}

// 移动端组件

// 移动端滚动显示Hook
function useScrollShow(delayOffset = 0) {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delayOffset);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(element);
    return () => observer.unobserve(element);
  }, [delayOffset]);

  return { ref, isVisible };
}

// 服务项组件
function ServiceItemComponent({ service }: { service: ServiceItem }) {
  const { ref, isVisible } = useScrollShow(100);

  return (
    <div
      ref={ref}
      className={`service-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <div className="figure-area">
        <figure>
          {service.isVideo ? (
            <video
              controls
              preload="auto"
              poster="/images/index/video-bg.jpg"
            >
              <source src={service.image} type="video/mp4" />
            </video>
          ) : (
            <Image
              src={service.image}
              alt={service.alt}
              width={640}
              height={360}
            />
          )}
        </figure>
      </div>
      <div className="service-content">
        <div className="service-title">
          <h3>{service.title}</h3>
        </div>
        <div className="service-desc">
          {service.description.map((paragraph, idx) => (
            <p key={idx}>
              {paragraph}
            </p>
          ))}
          <button
            className="service-contact-button"
            onClick={() => {
              // This will be handled by parent component
              const event = new CustomEvent('showContactForm');
              window.dispatchEvent(event);
            }}
          >
            Request More Information
            <svg className="inline-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

// 优势项组件
function AdvantageItemComponent({ advantage, index }: { advantage: AdvantageItem; index: number }) {
  const { ref, isVisible } = useScrollShow(index * 100);

  return (
    <div
      ref={ref}
      className={`advantage-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <figure className="advantage-icon-area">
        <Image
          src={advantage.icon}
          alt={advantage.alt}
          width={32}
          height={32}
          className="w-8 h-8"
        />
      </figure>
      <div className="advantage-title">
        {advantage.title}
      </div>
      <div className="advantage-content">
        {advantage.description}
      </div>
    </div>
  );
}

// 流程项组件
function ProcessItemComponent({ item, index }: { item: ProcessItem; index: number }) {
  const { ref, isVisible } = useScrollShow(index * 100);

  return (
    <div
      ref={ref}
      className={`process-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <div className="process-background">
        <div className={`process-head-wrapper flex ${index % 2 === 1 ? 'flex-row-reverse' : ''} items-center`}>
          <figure className="flex-1">
            <Image
              src={item.image}
              alt={item.alt}
              width={320}
              height={180}
              unoptimized
              className="w-full rounded-xl"
            />
          </figure>
          <div className={`process-num ${index % 2 === 1 ? 'mr-4' : 'ml-4'}`}>
            {item.num}
          </div>
        </div>
      </div>
      <div className="process-content">
        <div className="process-title">
          <h3>
            {item.title}
          </h3>
        </div>
        <div className="process-desc">
          <p>{item.description}</p>
        </div>
      </div>
    </div>
  );
}

// 解决方案案例组件
function SolutionItemComponent({
  caseItem,
  solutionExpandStatus,
  toggleSolution,
  getSolutionDescClass,
  expandText,
  collapseText
}: {
  caseItem: SolutionCase;
  solutionExpandStatus: Record<string, boolean>;
  toggleSolution: (id: string) => void;
  getSolutionDescClass: (id: string) => string;
  expandText: string;
  collapseText: string;
}) {
  const { ref, isVisible } = useScrollShow(180);

  return (
    <div
      ref={ref}
      className={`solution-item ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <div className="solution-wrapper">
        <div className="solution-figure">
          <figure>
            <Image
              src={caseItem.image}
              alt={caseItem.title}
              width={640}
              height={200}
              className=""
            />
          </figure>
        </div>
        <div className="solution-content">
          <div className="solution-title">
            <h3>
              {caseItem.title}
            </h3>
          </div>
          <div
            className={getSolutionDescClass(caseItem.id)}
          >
            <div>
              <div>
                <h4>Background & Challenges</h4>
                <p>{caseItem.background}</p>
                <ul>
                  {caseItem.challenges.map((challenge: string, idx: number) => (
                    <li key={idx}>{challenge}</li>
                  ))}
                </ul>
                {caseItem.keyProblems && (
                  <div>
                    <h4>Key challenges faced include:</h4>
                    <ul>
                      {caseItem.keyProblems.map((problem: string, idx: number) => (
                        <li key={idx}>{problem}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div>
                <h4>Solutions Provided by SmartDeer</h4>
                <div>
                  {caseItem.solutions.map((solution, idx) => (
                    <div key={idx}>
                      <strong>{solution.title}</strong>
                      <ul>
                        {solution.items.map((item: string, itemIdx: number) => (
                          <li key={itemIdx}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4>Results Achieved</h4>
                <ul>
                  {caseItem.results.map((result: string, idx: number) => (
                    <li key={idx}>{result}</li>
                  ))}
                </ul>
              </div>

              <div>
                <h4>Client Testimonial</h4>
                <p>{caseItem.testimonial}</p>
              </div>
            </div>
          </div>
          <div className="solution-expand">
            <button
              onClick={() => toggleSolution(caseItem.id)}
              className="solution-toggle"
            >
              <span>{!solutionExpandStatus[caseItem.id] ? expandText : collapseText}</span>
              <svg className="inline-arrow" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d={!solutionExpandStatus[caseItem.id] ? "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" : "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"} clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// 移动端英文版本主组件
export default function HomePageClientMobile({ locale }: HomePageClientMobileProps) {
  const [showContactForm, setShowContactForm] = useState(false);
  const [showConsultantCode, setShowConsultantCode] = useState(false);
  const [solutionExpandStatus, setSolutionExpandStatus] = useState<Record<string, boolean>>({
    s1: false,
    s2: false,
    s3: false,
    s4: false,
    s5: false,
    s6: false
  });

  // 获取当前语言的内容
  const content = getContent(locale);
  const [lifecycleItems, setLifecycleItems] = useState([0, 1, 2, 3, 4, 5, 6]);

  // 生命周期轮播效果
  useEffect(() => {
    const interval = setInterval(() => {
      setLifecycleItems(prev => prev.map(item => (item + 1) % 7));
    }, 2200);
    return () => clearInterval(interval);
  }, []);

  // 监听联系表单显示事件
  useEffect(() => {
    const handleShowContactForm = () => {
      setShowContactForm(true);
    };

    window.addEventListener('showContactForm', handleShowContactForm);
    return () => {
      window.removeEventListener('showContactForm', handleShowContactForm);
    };
  }, []);

  // 滚动监听
  useEffect(() => {
    const scrollItems: Array<{ offsetTop: number; el: HTMLElement }> = [];
    let timer: NodeJS.Timeout | null = null;

    const scroll = () => {
      if (timer) return;
      timer = setTimeout(() => {
        const offset = window.scrollY + window.innerHeight;
        scrollItems.forEach((item, index) => {
          if (item.offsetTop < offset) {
            item.el.setAttribute('show', 'true');
            scrollItems.splice(index, 1);
          }
        });
        timer = null;
      }, 30);
    };

    window.addEventListener('scroll', scroll);
    return () => {
      window.removeEventListener('scroll', scroll);
      if (timer) clearTimeout(timer);
    };
  }, []);

  // 聊天机器人
  const cozeWebSDK = useRef<unknown>(null);
  useEffect(() => {
    // 初始化聊天机器人
    if (typeof window !== 'undefined' && (window as unknown as { CozeWebSDK?: unknown }).CozeWebSDK) {
      cozeWebSDK.current = new ((window as unknown as { CozeWebSDK: { WebChatClient: new (config: unknown) => unknown } }).CozeWebSDK.WebChatClient)({
        config: {
          botId: '7439335660751716386'
        },
        ui: {
          base: {
            icon: 'https://static.smartdeer.com/bot_logo.png',
            layout: 'mobile',
            zIndex: 1000
          },
          chatBot: {
            title: '顾问杰哥',
            uploadable: false,
          },
          asstBtn: {
            isNeed: false
          },
          footer: {
            isShow: true,
            expressionText: 'Powered by SmartDeer.'
          }
        }
      });
    }
  }, []);

  const toggleChat = () => {
    if (cozeWebSDK.current && typeof cozeWebSDK.current === 'object' && cozeWebSDK.current !== null && 'showChatBot' in cozeWebSDK.current) {
      (cozeWebSDK.current as { showChatBot: () => void }).showChatBot();
    }
  };

  const toggleSolution = (key: string) => {
    setSolutionExpandStatus(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const getSolutionDescClass = useCallback((key: string) => {
    const isExpanded = solutionExpandStatus[key];
    return `solution-desc ${isExpanded ? 'expanded' : ''}`;
  }, [solutionExpandStatus]);

  const smoothScrollTo = (duration: number, target: number) => {
    const start = window.scrollY;
    const startTime = performance.now();

    function scrollStep(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const newScrollY = start + (target - start) * progress;
      window.scrollTo(0, newScrollY);
      if (progress < 1) {
        requestAnimationFrame(scrollStep);
      }
    }
    requestAnimationFrame(scrollStep);
  };

  const submitSuccess = () => {
    setShowContactForm(false);
    // 这里可以添加成功提示
  };

  // 滚动到元素
  const scrollToElement = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // 根据语言版本渲染不同的头部结构
  const renderHeader = () => {
    if (locale === 'zh') {
      // 中文版本：使用v1的头部横幅结构
      return (
        <header>
          <div className="header-content">
            <div className="header-banner">
              <div className="header-banner-text">
                <div className="slogon">全球招&全球雇</div>
                <h1 className="site-title">全球人力资源一站式服务</h1>
                <div className="desc">
                  <div className="desc-text">
                    招纳全球优秀人才，处理全球雇佣合规与薪酬发放问题，提供专业的人力资源一站式服务解决方案。
                  </div>
                </div>
              </div>
              <div className="header-banner-image">
                <figure>
                  <Image
                    src="/images/index/globle-min.webp"
                    alt="Global Service"
                    width={187}
                    height={150}
                  />
                </figure>
              </div>
            </div>
          </div>
        </header>
      );
    } else {
      // 英文和日文版本：使用简化的头部结构
      const headerContent = locale === 'en' ? {
        slogon: 'Global Recruitment & Employment',
        title: 'International HR One-stop Service',
        description: 'Global recruitment hires the worldwide talents, we handle the compliance of global recruitment and salary and payroll, provide professional one-stop Human Resources solution.'
      } : {
        slogon: 'グローバル採用・海外雇用支援',
        title: '海外人材採用・雇用のワンストップサービス',
        description: '世界中から優秀な人材を採用し、各国の雇用・給与に関するコンプライアンス対応を含む、プロフェッショナルな人事ソリューションをワンストップで提供します。'
      };

      return (
        <header>
          <div className="header-content">
            <div className="slogon">{headerContent.slogon}</div>
            <h1 className="site-title">{headerContent.title}</h1>
            <div className="desc">
              <div className="desc-text">{headerContent.description}</div>
              <div className="image">
                <figure>
                  <Image
                    src="/images/index/globle-min.webp"
                    alt="Global Service"
                    width={187}
                    height={150}
                  />
                </figure>
              </div>
            </div>
          </div>
        </header>
      );
    }
  };

  return (
    <div className="index-page">
      {renderHeader()}

      {/* Customer Section */}
      <section className="customer">
        <div className="section-title">
          <h2>
            {locale === 'zh' ? '服务全球海量客户' :
             locale === 'en' ? 'Serve Global Customers' :
             '世界中のお客様をサポート'}
          </h2>
          {locale === 'zh' && <p>Serve Global Customers</p>}
        </div>
        <CustomerList />
      </section>

      {/* Service Section */}
      <section className="service">
        <div className="section-title">
          <h2>
            {locale === 'zh' ? '全球人力服务 & 全球 HR Saas' :
             locale === 'en' ? 'Our Solutions' :
             'ソリューション'}
          </h2>
          {locale === 'zh' && <p>Our Solutions & Global Saas</p>}
          {locale === 'en' && <p></p>}
          {locale === 'ja' && <p></p>}
        </div>
        <div className="service-list">
          {content.services.items.map((service) => (
            <ServiceItemComponent key={service.id} service={service} />
          ))}
        </div>
      </section>

      {/* Advantage Section */}
      <section className="advantage">
        <div className="section-title">
          <h2>
            {locale === 'zh' ? '核心优势' :
             locale === 'en' ? 'Why choose us?' :
             'なぜ私たちを選ぶのか？'}
          </h2>
          {locale === 'zh' && <p>Why choose us?</p>}
        </div>
        <div className="advantage-list">
          {content.advantages.items.map((advantage, index) => (
            <AdvantageItemComponent key={index} advantage={advantage} index={index} />
          ))}
        </div>
      </section>

      {/* Lifecycle Section */}
      <section className="lifecycle">
        <div className="section-title">
          <h2>
            {locale === 'zh' ? '全生命周期管理' :
             locale === 'en' ? 'Full-Life Cycle Management' :
             'フルライフサイクル管理'}
          </h2>
          {locale === 'zh' && <p>Full-Life Cycle Management</p>}
        </div>
        <div className="lifecycle-list">
          <div className="lifecycle-list-container">
            {content.lifecycle.items.map((item, index) => {
              const topOffset = [0, 20, 40, 60, 40, 20, 0][lifecycleItems[index]] || 0;
              return (
                <div
                  key={index}
                  className="lifecycle-item"
                  style={{ top: `${topOffset}px` }}
                >
                  <figure className="lifecycle-item-icon">
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={40}
                      height={40}
                    />
                    {index < 6 && (
                      <div
                        className="arrow"
                        style={{ backgroundImage: 'url("/images/index/arrow.svg")' }}
                      />
                    )}
                  </figure>
                  <div className="lifecycle-item-title">
                    <h3>
                      {item.title}
                    </h3>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="process">
        <div className="section-title">
          <h2>{content.process.title}</h2>
        </div>
        <div className="process-list">
          {content.process.items.map((item, index) => (
            <ProcessItemComponent key={index} item={item} index={index} />
          ))}
        </div>
      </section>

      {/* Solution Cases Section */}
      <section className="solution">
        <div className="section-title">
          <h2>{content.solution.title}</h2>
          <p>{content.solution.subtitle}</p>
        </div>
        <div className="solution-list">
          {content.solution.cases.map((caseItem) => (
            <SolutionItemComponent
              key={caseItem.id}
              caseItem={caseItem}
              solutionExpandStatus={solutionExpandStatus}
              toggleSolution={toggleSolution}
              getSolutionDescClass={getSolutionDescClass}
              expandText={content.solution.expandText}
              collapseText={content.solution.collapseText}
            />
          ))}
        </div>
      </section>

      {/* Contact Form */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <ContactForm
              locale={locale}
              onClose={() => setShowContactForm(false)}
              onSubmit={submitSuccess}
            />
          </div>
        </div>
      )}

      {/* Consultant Code */}
      <div className="anchor fixed right-5 bottom-[186px] z-[99]">
        <div
          className="consultant w-[73px] h-[81px] cursor-pointer"
          onClick={() => setShowConsultantCode(!showConsultantCode)}
        >
          <figure className="w-full h-full">
            <Image
              src="/images/index/anchor-avatar-en.png"
              alt="Consultant"
              width={73}
              height={81}
              className="block w-full h-full"
            />
          </figure>
        </div>
        {showConsultantCode && (
          <div className="consultant-code w-[236px] h-[321px] fixed right-[83px] bottom-[169px]">
            <div
              className="close w-5 h-5 cursor-pointer absolute top-[27px] right-[35px]"
              onClick={() => setShowConsultantCode(false)}
            />
            <figure className="w-full h-full">
              <Image
                src="/images/index/anchor-code-en.png"
                alt="Consultant Code"
                width={236}
                height={321}
                className="block w-full h-full"
              />
            </figure>
          </div>
        )}
      </div>

      {/* Bot Button */}
      <div
        className="bot-container fixed right-5 bottom-[100px] z-[99] cursor-pointer"
        onClick={toggleChat}
      >
        <Image
          src="/images/index/bot_logo_en.png"
          alt="Chat Bot"
          width={60}
          height={60}
        />
      </div>

      {/* Go Top Button */}
      <div
        className="go-top-container fixed right-5 bottom-[30px] z-[99] cursor-pointer"
        onClick={() => smoothScrollTo(500, 0)}
      >
        <Image
          src="/images/index/top_icon.png"
          alt="Go Top"
          width={40}
          height={40}
        />
      </div>
    </div>
  );
}
