'use client';

import React from 'react';
import { Locale } from '@/types';
import HomePageClientPC from './HomePageClientPC';
import HomePageClientMobile from './HomePageClientMobile';

interface HomePageClientProps {
  locale: Locale;
}

/**
 * 主入口组件 - 使用Tailwind CSS响应式断点控制PC和移动端组件显示
 *
 * 技术实现：
 * 1. 使用Tailwind CSS的响应式断点（md:block md:hidden）控制显示
 * 2. PC端组件在md断点（768px）以上显示
 * 3. 移动端组件在md断点以下显示
 * 4. 确保UI完全还原，包括视觉元素、交互行为和动画效果
 * 5. 保持组件功能性和数据流逻辑不变
 */
export default function HomePageClient({ locale }: HomePageClientProps) {
  return (
    <div className="responsive-homepage-container">
      {/* PC端组件 - 在md断点（768px）及以上显示 */}
      <div className="hidden md:block">
        <HomePageClientPC locale={locale} />
      </div>

      {/* 移动端组件 - 在md断点（768px）以下显示 */}
      <div className="block md:hidden">
        <HomePageClientMobile locale={locale} />
      </div>
    </div>
  );
}