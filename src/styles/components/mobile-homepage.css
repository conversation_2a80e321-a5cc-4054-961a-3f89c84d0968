/* 移动端首页特定样式 - 基于v1版本 */

/* 移动端头部样式 */
@media (max-width: 768px) {
  .index-page header {
    background: #fff6ec;
    padding: 20px 0;
  }

  .index-page .header-content {
    max-width: 100%;
    padding: 0 20px;
  }

  /* 中文版本的头部横幅结构 */
  .index-page .header-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .index-page .header-banner-text {
    order: 1;
    margin-bottom: 20px;
  }

  .index-page .header-banner-image {
    order: 2;
  }

  /* 英文和日文版本的简化头部结构 */
  .index-page .slogon {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
  }

  .index-page .site-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
    line-height: 1.3;
  }

  .index-page .desc {
    text-align: center;
    margin-bottom: 20px;
  }

  .index-page .desc-text {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
  }

  .index-page .desc .image {
    display: flex;
    justify-content: center;
  }

  /* 客户部分 */
  .index-page .customer {
    padding: 40px 20px;
  }

  .index-page .customer .section-title h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .index-page .customer .section-title p {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  /* 服务部分 */
  .index-page .service {
    padding: 40px 20px;
  }

  .index-page .service .section-title h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .index-page .service .section-title p {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  .index-page .service-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .index-page .service-item {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .index-page .service-item .figure-area {
    order: 1;
    margin-bottom: 15px;
    text-align: center;
  }

  .index-page .service-item .service-content {
    order: 2;
  }

  .index-page .service-item .service-title h3 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .index-page .service-item .service-title p {
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
  }

  .index-page .service-item .service-desc p {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 10px;
  }

  .index-page .service-contact-button {
    background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 15px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }

  /* 优势部分 */
  .index-page .advantage {
    padding: 40px 20px;
    background: #f8f9fa;
  }

  .index-page .advantage .section-title h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .index-page .advantage .section-title p {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  .index-page .advantage-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .index-page .advantage-item {
    text-align: center;
    padding: 20px 10px;
    background: white;
    border-radius: 8px;
  }

  .index-page .advantage-icon-area {
    margin-bottom: 10px;
  }

  .index-page .advantage-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .index-page .advantage-content {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }

  /* 生命周期部分 */
  .index-page .lifecycle {
    padding: 40px 20px;
  }

  .index-page .lifecycle .section-title h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .index-page .lifecycle .section-title p {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  .index-page .lifecycle-list-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    position: relative;
  }

  .index-page .lifecycle-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 80px;
    position: relative;
  }

  .index-page .lifecycle-item-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }

  .index-page .lifecycle-item-title {
    font-size: 12px;
    font-weight: 500;
    color: #333;
  }

  /* 流程部分 */
  .index-page .process {
    padding: 40px 20px;
    background: #f8f9fa;
  }

  .index-page .process .section-title h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .index-page .process .section-title p {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
  }

  .index-page .process-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .index-page .process-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
  }

  .index-page .process-head {
    flex-shrink: 0;
    width: 60px;
    text-align: center;
  }

  .index-page .process-head figure {
    width: 50px;
    height: 50px;
    margin: 0 auto 5px;
  }

  .index-page .process-head .num {
    font-size: 12px;
    font-weight: bold;
    color: #f54a25;
  }

  .index-page .process-content {
    flex: 1;
  }

  .index-page .process-content .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }

  .index-page .process-content p {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
  }
}
