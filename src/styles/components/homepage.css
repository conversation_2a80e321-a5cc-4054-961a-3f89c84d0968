/* 首页组件特定样式 */

/* 首页样式 - 使用高优先级 */
.index-page {
  font-family: Helvetica;
  min-height: 100vh;
}

.index-page main section {
  margin-bottom: 160px;
}

.index-page main section:last-child {
  margin-bottom: 0;
}

.index-page section .section-title {
  text-align: center;
  margin-bottom: 40px;
}

.index-page section .section-title h2 {
  font-size: 48px;
  font-weight: 500;
  line-height: 67px;
  font-weight: bold;
}

.index-page section .section-title p {
  font-size: 18px;
  color: #999999;
  letter-spacing: 5px;
  margin-top: 8px;
  line-height: 22px;
  margin-bottom: 0;
}

/* Header Banner 样式 - 覆盖Tailwind */
.header-banner {
  background-color: #FFF6EC;
  height: 800px;
  margin-bottom: 180px;
  overflow: hidden;
  position: relative;
  padding: 0;
}

.header-banner-content {
  width: 1204px;
  box-sizing: border-box;
  margin: 0 auto;
  display: flex;
  margin-top: 8px;
  padding-top: 0;
}

.header-banner-image {
  user-select: none;
  position: relative;
  width: 638px;
  min-width: 638px;
  height: 637px;
  /* margin-left: 172px; */
  padding: 19px;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.header-banner-image .global-min_1 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 598px;
  height: 600px;
  margin-top: -300px;
  margin-left: -299px;
  z-index: 1;
  animation: aniRotate 18s linear infinite;
  animation-fill-mode: forwards;
}

.header-banner-image .global-min_1 img {
  transform: rotate(45deg);
  width: 100%;
  height: 100%;
  display: block;
}

.header-banner-image .global-min_2 {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 480px;
  height: 480px;
  margin-top: -240px;
  margin-left: -240px;
  animation: aniRotate 30s linear infinite;
  animation-fill-mode: forwards;
}

.header-banner-image .global-min_2 img {
  width: 100%;
  height: 100%;
  display: block;
  animation: 30s linear infinite spin;
}

.header-banner-image .global-min_3 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 598px;
  height: 600px;
  margin-top: -300px;
  margin-left: -299px;
  z-index: 3;
}

.header-banner-image .global-min_3 img {
  width: 559px;
  height: 556px;
  display: block;
  position: absolute;
  top: 19px;
  left: -10px;
}

.header-banner-text .slogon {
  color: #000;
  font-size: 58px;
  font-weight: 700;
  line-height: 78px;
  padding-top: 97px;
  width: 640px;
}

.header-banner-text .slogonZh {
  font-family: PingFangSC-Semibold, PingFang SC;
  height: 162px;
  color: #000000;
  line-height: 100px;
  letter-spacing: 8px;
  padding-top: 137px;
  font-size: 60px;
  font-weight: 600;
  white-space: nowrap;
}

.header-banner-text .title {
  letter-spacing: 1px;
  font-size: 42px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 0;
  padding: 0;
  margin-top: 15px;
  line-height: 68px;
}

.header-banner-text .titleEN {
  font-size: 36px;
}

.header-banner-text .desc {
  width: 641px;
  color: #000000;
  position: relative;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 18px;
  height: 117px;
  letter-spacing: 1px;
  line-height: 29px;
  margin-top: 20px;
  width: 476px;
}

.header-banner-text .desc span {
  position: relative;
  z-index: 1;
}

.header-banner-text .desc figure {
  position: absolute;
  left: -56px;
  top: -6px;
}

.header-banner-text .desc figure img {
  width: 140px;
  height: 140px;
}

/* Section 样式 - 覆盖Tailwind */
.section-title {
  text-align: center;
  margin-bottom: 40px;
  padding: 0;
}

.section-title h2 {
  font-size: 48px;
  font-weight: bold;
  line-height: 67px;
  color: #333333;
  margin: 0;
  padding: 0;
}

.section-title p {
  font-size: 18px;
  color: #999999;
  letter-spacing: 5px;
  margin-top: 8px;
  line-height: 22px;
  margin-bottom: 0;
  padding: 0;
}

/* Customer 样式 */
.customer {
  width: 1204px;
  margin: 0 auto;
}
