/* 计算器组件特定样式 */

/* Calculator Page Styles */
.contries-page {
  font-family: Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, Helvetica Neue, Arial, sans-serif;
  min-height: 100vh;
}

.contries-page header {
  background: #fff6ec;
}

.contries-page .header-banner {
  position: relative;
  width: 90vw;
  min-width: 0;
  height: auto;
  margin: 0 auto;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
  position: static;
}

.contries-page .header-banner-text {
  position: static;
  width: 100%;
  margin-top: 40px;
}

.contries-page .header-banner-text .header-title {
  font-size: 24px;
  margin-top: 0;
  text-align: center;
  font-weight: bold;
}

.contries-page .header-banner-text .header-desc {
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  font-weight: 300;
}

.contries-page .header-form {
  position: static;
  width: 100%;
  margin: 24px auto;
  padding: 24px 10px;
  box-shadow: none;
  border-radius: 16px;
  background: #fff;
  border: 1px #eff0f6 solid;
}

.contries-page .calculator-submit {
  color: #fff;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.contries-page .calculator-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.contries-page .form-item {
  margin-bottom: 16px;
}

.contries-page .form-select,
.contries-page .form-input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.contries-page .errorMessage {
  color: red;
  margin: 20px 0;
  font-size: 14px;
}

.contries-page .calculator-container {
  width: 90vw;
  min-width: 0;
  padding: 0 10px;
  box-sizing: border-box;
  margin: 0 auto;
}

.contries-page .calculator-container .result-title {
  font-size: 22px;
  margin-top: 50px;
  text-align: center;
}

.contries-page .calculator-container .result-description {
  font-size: 14px;
  text-align: center;
  color: #6f6c90;
  line-height: 20px;
}

.contries-page .result-table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 20px;
}

.contries-page .result-table tr,
.contries-page .result-table th,
.contries-page .result-table td {
  border: 1px #dbdee5 solid;
}

.contries-page .result-table td {
  width: 50%;
  font-size: 12px;
  padding: 12px 8px;
  color: #170f49;
  font-weight: 500;
}

.contries-page .cost-name-detail,
.contries-page .cost-amount-detail {
  background: rgba(239, 239, 239, 0.3);
}

.contries-page .cost-position {
  position: relative;
}

.contries-page .cost-position .icon {
  cursor: pointer;
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  right: 18px;
  top: 20px;
}

.contries-page .result-notice {
  color: #6f6c90;
  font-size: 12px;
  line-height: 16px;
  margin-top: 15px;
}

.contries-page .entity-notice {
  margin-top: 68px;
  font-size: 18px;
  font-weight: 400;
  color: #170f49;
}

.contries-page .calculator-contact {
  display: block;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border-radius: 20px;
  width: 100%;
  height: 40px;
  color: #fff;
  border: 0;
  background: linear-gradient(252deg, #f54a25 -45%, #ffab71 98%);
  box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
  margin: 20px auto 40px;
}

.contries-page .periodChange {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  margin-top: 20px;
}

.contries-page .el-dropdown-time {
  display: flex;
  gap: 10px;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

/* Calculator Results */
.calculator-results {
  max-width: 1204px;
  margin: 60px auto;
  padding: 0 20px;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.result-table td {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.result-table tr:last-child td {
  border-bottom: none;
}

.cost-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  width: 60%;
}

.cost-amount {
  font-size: 18px;
  font-weight: bold;
  color: #ff8600;
  text-align: right;
}

.cost-name-detail {
  font-size: 14px;
  color: #666;
  padding-left: 40px;
}

.cost-amount-detail {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.cost-position {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cost-position .icon {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.periodChange {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-dropdown-time {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  background: white;
  font-size: 14px;
}

.entity-notice {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 40px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.result-notice {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  line-height: 1.4;
}

.calculator-contact {
  background: linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
  margin: 20px auto;
  display: block;
}

.calculator-contact:hover {
  transform: translateY(-1px);
}

.no-result {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
}

.result-title {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 16px;
}

.result-description {
  font-size: 18px;
  text-align: center;
  color: #666;
  margin-bottom: 48px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 8px;
}

/* PC端响应式样式 - 768px及以上 */
@media (min-width: 768px) {
  .contries-page .header-banner {
    width: 100%;
    max-width: 1200px;
    flex-direction: row;
    align-items: flex-start;
    padding: 60px 40px;
    gap: 60px;
  }

  .contries-page .header-banner-text {
    flex: 1;
    margin-top: 0;
    padding-right: 40px;
  }

  .contries-page .header-banner-text .header-title {
    font-size: 48px;
    text-align: left;
    line-height: 1.2;
    margin-bottom: 24px;
  }

  .contries-page .header-banner-text .header-desc {
    font-size: 18px;
    line-height: 1.6;
    text-align: left;
    color: #666;
    max-width: 500px;
  }

  .contries-page .header-form {
    flex: 0 0 400px;
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-top: 0;
  }

  .contries-page .calculator-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #333;
  }

  .contries-page .calculator-desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 32px;
    line-height: 1.5;
  }

  .contries-page .form-body {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .contries-page .form-item {
    margin-bottom: 0;
  }

  .contries-page .form-select,
  .contries-page .form-input {
    height: 48px;
    font-size: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0 16px;
    transition: border-color 0.2s;
  }

  .contries-page .form-select:focus,
  .contries-page .form-input:focus {
    outline: none;
    border-color: #f54a25;
  }

  .contries-page .form-submit {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;
    margin-top: 8px;
  }

  .contries-page .form-submit:hover:not(:disabled) {
    transform: translateY(-1px);
  }

  .contries-page .form-submit:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
  }

  /* 结果区域PC端样式 */
  .contries-page .result-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 40px;
  }

  .contries-page .result-title {
    font-size: 36px;
    margin-bottom: 20px;
  }

  .contries-page .result-description {
    font-size: 18px;
    margin-bottom: 40px;
  }

  .contries-page .view-toggle {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 40px;
  }

  .contries-page .view-toggle button {
    padding: 12px 24px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .contries-page .view-toggle button.active {
    background: #f54a25;
    color: white;
    border-color: #f54a25;
  }

  .contries-page .result-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .contries-page .result-table table {
    width: 100%;
    border-collapse: collapse;
  }

  .contries-page .result-table th,
  .contries-page .result-table td {
    padding: 16px 24px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  .contries-page .result-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #2d3748;
  }

  .contries-page .result-table .amount {
    font-weight: 600;
    color: #f54a25;
  }

  .contries-page .entity-notice {
    max-width: 800px;
    margin: 40px auto;
    text-align: center;
    font-size: 16px;
    color: #666;
    line-height: 1.6;
  }

  .contries-page .calculator-contact {
    font-size: 18px;
    padding: 16px 32px;
    margin: 40px auto;
  }
}
