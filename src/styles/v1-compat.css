/* V1 版本兼容样式 - 使用更高优先级覆盖Tailwind */

/* 字体定义 */
@font-face {
  font-family: Helvetica;
  src: url('/fonts/Helvetica.ttf') format('truetype');
}

/* 旋转动画 */
@keyframes aniRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 重置样式，匹配 v1 版本 */
html, body {
  padding: 0 ;
  margin: 0 ;
  font-family: Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, Helvetica Neue, Arial, sans-serif ;
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
}

figure {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

img {
  display: block;
}

ul {
  margin-block-start: 0;
  margin-block-end: 0;
  padding-inline-start: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
  margin-block-start: 0;
  margin-block-end: 0;
}

a, a:active {
  color: #FF8600;
  text-decoration: none;
}

/* 首页样式 - 使用高优先级 */
.index-page {
  font-family: Helvetica ;
  min-height: 100vh ;
}

.index-page main section {
  margin-bottom: 160px;
  &:last-child {
    margin-bottom: 0;
  }
}

.index-page section .section-title {
  text-align: center;
  margin-bottom: 40px;
}

.index-page section .section-title h2 {
  font-size: 48px;
  font-weight: 500;
  line-height: 67px;
  font-weight: bold;
}

.index-page section .section-title p {
  font-size: 18px;
  color: #999999;
  letter-spacing: 5px;
  margin-top: 8px;
  line-height: 22px;
  margin-bottom: 0;
}

/* Header Banner 样式 - 覆盖Tailwind */

.index-page main section {
  margin-bottom: 160px;
}

.header-banner {
  background-color: #FFF6EC ;
  height: 800px ;
  margin-bottom: 180px ;
  overflow: hidden ;
  position: relative ;
  padding: 0 ;
}

.header-banner-content {
  width: 1204px ;
  box-sizing: border-box ;
  margin: 0 auto ;
  display: flex ;
  margin-top: 8px ;
  padding-top: 0 ;
}



.header-banner-image {
  user-select: none;
  position: relative;
  width: 638px;
  min-width: 638px;
  height: 637px;
  /* margin-left: 172px; */
  min-width: 638px;
  padding: 19px;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.header-banner-image .global-min_1 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 598px;
  height: 600px;
  margin-top: -300px;
  margin-left: -299px;
  z-index: 1;
  animation: aniRotate 18s linear infinite;
  animation-fill-mode: forwards;
}

.header-banner-image .global-min_1 img {
  transform: rotate(45deg);
  width: 100%;
  height: 100%;
  display: block;
}

.header-banner-image .global-min_2 {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 480px;
  height: 480px;
  margin-top: -240px;
  margin-left: -240px;
  animation: aniRotate 30s linear infinite;
  animation-fill-mode: forwards;
}

.header-banner-image .global-min_2 img {
  width: 100%;
  height: 100%;
  display: block;
  animation: 30s linear infinite spin;
}

.header-banner-image .global-min_3 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 598px;
  height: 600px;
  margin-top: -300px;
  margin-left: -299px;
  z-index: 3;
}

.header-banner-image .global-min_3 img {
  width: 559px;
  height: 556px;
  display: block;
  position: absolute;
  top: 19px;
  left: -10px;
}

.header-banner-text .slogon{
  color: #000;
  font-size: 58px;
  font-weight: 700;
  /* height: 162px; */
  line-height: 78px;
  padding-top: 97px;
  width: 640px;
}

.header-banner-text .slogonZh {
  font-family: PingFangSC-Semibold, PingFang SC;
  height: 162px;
  color: #000000 ;
  line-height: 100px ;
  letter-spacing: 8px;
  padding-top: 137px;
  font-size: 60px;
  font-weight: 600;
  white-space: nowrap;
}

.header-banner-text .title {
 letter-spacing: 1px;
  font-size: 42px ;
  font-weight: bold ;
  color: #000000 ;
  margin-bottom: 0 ;
  padding: 0 ;
  margin-top: 15px;
  line-height: 68px;
}

.header-banner-text .titleEN {
font-size: 36px ;
}

.header-banner-text .desc {
  width: 641px ;
  color: #000000 ;
  position: relative ;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 18px;
  height: 117px;
  letter-spacing: 1px;
  line-height: 29px;
  margin-top: 20px;
  width: 476px;
}

.header-banner-text .desc span {
  position: relative;
  z-index: 1;
}

.header-banner-text .desc figure {
  position: absolute;
  left: -56px;
  top: -6px;
}

.header-banner-text .desc figure img {
  width: 140px;
  height: 140px;
}

/* Main sections */
main section {
  margin-bottom: 160px ;
}

main section:last-child {
  margin-bottom: 0 ;
}

/* Section 样式 - 覆盖Tailwind */
.section-title {
  text-align: center ;
  margin-bottom: 40px ;
  padding: 0 ;
}

.section-title h2 {
  font-size: 48px ;
  font-weight: bold ;
  line-height: 67px ;
  color: #333333 ;
  margin: 0 ;
  padding: 0 ;
}

.section-title p {
  font-size: 18px ;
  color: #999999 ;
  letter-spacing: 5px ;
  margin-top: 8px ;
  line-height: 22px ;
  margin-bottom: 0 ;
  padding: 0 ;
}

/* Customer 样式 */
.customer {
  width: 1204px;
  margin: 0 auto;
}

/* Service 样式 - 覆盖Tailwind */
.service {
  overflow: hidden ;
  min-width: 1280px ;
  /* background: transparent ; */
  padding: 0 ;
}

.service-list {
  width: 1204px ;
  margin: 0 auto ;
  padding: 0 ;
}

.service-item {
  display: flex ;
  justify-content: space-between ;
  padding: 58px 0px ;
  position: relative ;
  margin-bottom: 0 ;
}

.service-item:nth-child(2n+1) .figure-area {
  right: -100px;
}

.service-item:nth-child(2n+1) .service-content {
  left: -100px;
}

.service-item:nth-child(2n) .figure-area {
  left: -100px;
}

.service-item:nth-child(2n) .service-content {
  right: -100px;
}

.service-item:nth-child(2n+1) .figure-area {
  right: 0px;
  opacity: 1;
}

.service-item:nth-child(2n+1) .service-content {
  left: 0px;
  opacity: 1;
}

.service-item:nth-child(2n) .figure-area {
  left: 0px;
  opacity: 1;
}

.service-item:nth-child(2n) .service-content {
  right: 0px;
  opacity: 1;
}

.service-item .figure-area {
  position: relative;
  opacity: 0;
  min-height: 333px;
  transition: all .5s;
}

.service-item .figure-area figure img,
.service-item .figure-area figure #video-player {
  width: 587px;
}

.service-item .figure-area figure #video-player {
  height: 333px;
  background: transparent;
}

.service-item .figure-area figure #video-player video {
  border-radius: 20px;
  overflow: hidden;
}

.service-content {
  position: relative;
  opacity: 0;
  width: 500px;
  flex: 0 0 auto;
  padding: 0 16px;
  box-sizing: border-box;
  transition: all .5s;
}

.service-title {
  color: #333333;
}

.service-title h3 {
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
  margin: 0;
}

.service-title p {
  font-size: 18px;
  color: #333333;
  letter-spacing: 0;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.service-desc {
  font-size: 16px;
  color: #454545;
  line-height: 27px;
  margin-top: 32px;
}

.service-desc p {
  text-indent: 1em;
  position: relative;
  margin: 0;
}

.service-desc p::before {
  content: '';
  width: 3px;
  height: 3px;
  background: #FF7F00;
  position: absolute;
  left: 0;
  top: 12px;
}

.service-contact .service-contact-button {
  background: transparent;
  line-height: 28px;
  font-size: 14px;
  border-radius: 20px;
  padding: 0 20px;
  border: 1px #000 solid;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.service-contact-button {
  background: transparent;
  line-height: 28px;
  font-size: 14px;
  border-radius: 20px;
  padding: 0 20px;
  border: 1px #000 solid;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s;
}

.service-contact-button:hover {
  background-color: #000;
  color: white;
}

.inline-arrow {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

/* Advantages 样式 */
.advantages {
  width: 1204px;
  margin: 0 auto;
}

.advantage-list {
  display: flex;
  justify-content: space-between;
}

.advantage-list .advantage-item {
  transform: scale(1);
  transition: all .4s;
}

.advantage-item {
  padding-bottom: 40px;
  width: 279px;
  background-color: #FEEFDF;
  border-radius: 18px;
  transform: scale(0);
  transition: all .4s;
  color: #333333;
  text-align: center;
  height: 318px;
  padding-top: 48px;
  box-sizing: border-box;
}

.advantage-icon-area {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.advantage-icon-area img {
  width: 64px;
}

.advantage-title {
  font-size: 28px;
  margin-top: 24px;
  line-height: 28px;
  font-weight: bold;
}

.advantage-content {
  font-size: 18px;
  margin: 0 auto;
  margin-top: 14px;
  width: 222px;
  line-height: 1.4;
}

/* Lifecycle 样式 */
.lifecycle {
  width: 1204px;
  margin: 0 auto;
}

.lifecycle-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 86px;
}

.lifecycle-list .lifecycle-item {
  left: 0;
  opacity: 1;
  transition: all .4s;
}

.lifecycle-item {
  position: relative;
  left: -40px;
  opacity: 0;
  text-align: center;
}

.lifecycle-item-icon {
  background: #FFFFFF;
  height: 152px;
  width: 152px;
  border-radius: 50%;
  margin-bottom: 32px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lifecycle-item-icon .arrow {
  background-image: url("/images/index/arrow.svg");
  height: 14px;
  width: 12px;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 67px;
  right: -18px;
}

.lifecycle-item-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  line-height: 32px;
  max-width: 120px;
  margin: 0 auto;
}

.lifecycle-repeat {
  width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.lifecycle-repeat img {
  width: 100%;
}

/* Process 样式 */
.process-list .process-item {
  position: relative;
  min-width: 1280px;
  margin-bottom: 160px;
  height: 506px;
}

.process-list .process-item .figure-area {
  top: 0;
  transition: all .8s;
  opacity: 1;
}

.process-list .process-item:last-child .process-content-wrapper {
  padding-bottom: 0;
}

.process-list .process-item:nth-child(6) .process-content-wrapper {
  position: relative;
  top: -46px;
}

.process-content-wrapper {
  width: 1253px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 506px;
}

.process-background {
  width: 50%;
  height: 506px;
  position: absolute;
  z-index: -1;
  
}

.process-background .process-num {
  font-family: Verdana;
  font-size: 64px;
  color: #333333;
  position: absolute;
  line-height: 1;
  top: 100px;
}

.process-background.layout-left {
  left: 0;
  background: linear-gradient(270deg, #FFFFFF 0%, #FFF2E3 100%);
}

.process-background.layout-left .process-num {
  right: 610px;
}

.process-background.layout-right {
  right: 0;
  background-image: linear-gradient(90deg, #FFFFFF 0%, #FFF2E3 100%);
}

.process-background.layout-right .process-num {
  left: 610px;
}

.process-item .figure-area {
  flex: 1 1 auto;
  display: flex;
  justify-content: center;
  position: relative;
  top: 120px;
  transition: all .8s;
  opacity: 0;
}

.process-item .figure-area img {
  width: 659px;
  /* 移除可能导致透明背景显示异常的drop-shadow滤镜 */
  /* filter: drop-shadow(0 2px 16px RGBA(0,0,0,.18)); */
  box-shadow: 0 2px 16px rgba(0,0,0,0.18);
  border-radius: 8px;
}

.process-content {
  margin-top: 48px;
  width: 500px;
  flex: 0 0 auto;
  padding: 0 8px;
  box-sizing: border-box;
}

.process-title h3 {
  font-size: 32px;
  font-weight: 500;
  line-height: 40px;
  font-weight: bold;
  color: #222222;
  margin: 0;
}

.process-title p {
  width: 484px;
  font-size: 16px;
  color: #454545;
  line-height: 26px;
  margin-top: 24px;
}

.process-desc {
  font-size: 16px;
  color: #666666;
  line-height: 27px;
}

.process-desc p {
  margin: 0;
}

/* Solution 样式 */
.solution {
  overflow: hidden;
  min-width: 1280px;
}

.solution-list {
  margin: 0 auto;
}

.solution-list .case1-bg {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(239, 223, 207, 0.4));
}

.solution-list .case2-bg {
  background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
}

.solution-list .case3-bg {
  background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
}

.solution-list .case4-bg {
  background: linear-gradient(to bottom, rgba(251, 235, 186, 0.04), rgba(251, 235, 186, 0.4));
}

.solution-list .case5-bg {
  background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
}

.solution-list .case6-bg {
  background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
}

.solution-item {
  padding: 68px 0;
}

.solution-item:nth-child(2n+1) .figure-area {
  right: -70px;
}

.solution-item:nth-child(2n+1) .service-content {
  left: -70px;
}

.solution-item:nth-child(2n) .figure-area {
  left: -70px;
}

.solution-item:nth-child(2n) .service-content {
  right: -70px;
}

.solution-item:nth-child(2n+1) .solution-figure {
  right: 0;
  opacity: 1;
}

.solution-item:nth-child(2n+1) .solution-content {
  left: 0;
  opacity: 1;
}

.solution-item:nth-child(2n) .solution-figure {
  left: 0;
  opacity: 1;
}

.solution-item:nth-child(2n) .solution-content {
  right: 0;
  opacity: 1;
}

.solution-wrapper {
  width: 1204px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}

.solution-figure {
  position: relative;
  opacity: 0;
  min-height: 333px;
  transition: all .5s;
}

.solution-figure figure img {
  width: 400px;
}

.solution-content {
  position: relative;
  opacity: 0;
  width: 734px;
  flex: 0 0 auto;
  box-sizing: border-box;
  transition: all .5s;
}

.solution-title {
  color: #333333;
}

.solution-title h3 {
  font-size: 30px;
  font-weight: 500;
  font-family: PingFangSC-Medium, PingFang SC;
  line-height: 40px;
  color: #333333;
  margin: 0;
}

.solution-title p {
  font-size: 18px;
  font-family: DIN-Regular, DIN;
  font-weight: 400;
  color: #999;
  line-height: 26px;
  margin: 10px 0 0 0;
}

.solution-desc {
  font-size: 16px;
  color: #333;
  line-height: 27px;
  overflow: hidden;
  max-height: 380px;
  transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.solution-desc.expanded {
  max-height: 2000px;
}

.solution-desc h4 {
  margin: 20px 0 10px 0;
  font-weight: bold;
}

.solution-desc ol {
  position: relative;
  padding-left: 20px;
  margin: 0;
}

.solution-desc ol li {
  list-style: decimal;
  position: relative;
}

.solution-desc ul {
  position: relative;
  padding-left: 20px;
  margin: 0;
}

.solution-desc ul li {
  position: relative;
}

.solution-desc li > ul {
  padding-left: 20px;
}

.solution-desc li > ul li {
  list-style: circle;
}

.solution-desc p {
  position: relative;
  margin: 0;
}

.solution-expand {
  margin-top: 20px;
}

.solution-toggle {
  background: transparent;
  border: 1px #000 solid;
  padding: 0 20px;
  line-height: 28px;
  border-radius: 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #000;
  font-size: 14px;
}

.solution-toggle:hover {
  background-color: #000;
  color: white;
}

/* Anchor 锚点样式 */
.anchor {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 99;
}

.anchor .consultant {
  width: 91px;
  height: 101px;
  cursor: pointer;
}

.anchor .consultant-code {
  width: 236px;
  height: 321px;
  position: fixed;
  right: 130px;
  bottom: 25px;
}

.anchor .consultant-code .close {
  width: 20px;
  height: 20px;
  cursor: pointer;
  position: absolute;
  top: 22px;
  right: 19px;
}

.anchor figure {
  width: 100%;
  height: 100%;
}

.anchor figure img {
  display: block;
  width: 100%;
  height: 100%;
}

/* Bot 聊天机器人样式 */
.bot-container {
  z-index: 1000;
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 124px;
  right: 50px;
  transition: transform 0.3s ease;
}

.bot-container:hover {
  transform: scale(1.16);
}

.bot-container img {
  width: 80px;
  height: 91px;
}

/* Go Top 回到顶部样式 */
.go-top-container {
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 30px;
  right: 55px;
}

.go-top-container img {
  width: 70px;
  height: 70px;
}

/* Contact Form 联系表单样式 */
.contact-form .form-title {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  margin-top: -30px;
}

.contact-form .form-body {
  padding: 0 80px;
}

.contact-form .form-body .mobile {
  line-height: 46px;
  border: 1px solid;
  border-radius: 8px;
  width: 100%;
}

/* 移动端样式 */
@media (max-width: 768px) {
  /* 重置移动端基础样式 */
  .index-page {
    min-width: 375px;
    font-family: Helvetica;
  }

  /* Header 移动端样式 */
  header {
    display: block;
    position: relative;
    background-color: #FFF3E6;
    width: 100%;
    overflow: hidden;
  }

  .header-content {
    width: auto;
    max-width: 680px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    padding-bottom: 37px;
  }

  .header-banner {
    display: flex;
    justify-content: space-between;
    margin-top: 29px;
  }

  .header-banner-text {
    padding-top: 14px;
  }

  .header-banner-text .slogon {
    font-size: 26px;
    font-weight: 600;
    line-height: 37px;
    font-family: PingFangSC-Semibold, PingFang SC;
    white-space: nowrap;
  }

  .header-banner-text h1.site-title {
    white-space: nowrap;
    font-size: 16px;
    font-weight: 600;
    color: #000000;
    line-height: 27px;
    margin-top: 10px;
    font-family: PingFangSC-Semibold, PingFang SC;
  }

  .header-banner-text .desc {
    font-size: 12px;
    line-height: 20px;
  }

  .header-banner-text .desc .desc-text {
    font-size: 12px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    line-height: 17px;
    margin-top: 10px;
    width: 174px;
    min-width: 174px;
  }

  .header-banner-image img {
    width: 187px;
  }

  /* Section 移动端样式 */
  main section {
    margin-bottom: 56px;
  }

  .section-title {
    text-align: center;
    line-height: normal;
    margin-bottom: 20px;
  }

  .section-title h2 {
    font-size: 20px;
    margin-bottom: 6px;
    line-height: 1.2;
  }

  .section-title p {
    font-size: 12px;
    color: #999999;
    line-height: 1.2;
    margin-top: 4px;
  }

  /* Customer 移动端样式 */
  section.customer {
    max-width: 680px;
    margin: 0 auto;
    padding: 0px 20px;
    box-sizing: border-box;
    margin-top: 39px;
  }

  /* Service 移动端样式 */
  section.service {
    max-width: 680px;
    margin: 0 auto;
    padding: 0px 20px;
    box-sizing: border-box;
    margin-top: 53px;
  }

  .service-item {
    margin-bottom: 30px;
  }

  .service-item .figure-area {
    margin-bottom: 20px;
  }

  .service-item .figure-area figure {
    display: block;
  }

  .service-item .figure-area img,
  .service-item .figure-area #video-player {
    width: 100%;
  }

  .service-item .figure-area #video-player {
    width: 100%;
    height: 212px;
    background: transparent;
  }

  .service-item .figure-area #video-player video {
    border-radius: 20px;
    overflow: hidden;
  }

  .service-item {
    margin-bottom: 32px;
    padding: 0;
    display: block;
    position: static;
    transition: all 0.5s;
  }

  .service-item .figure-area {
    display: none;
  }

  .service-content {
    position: static;
    opacity: 1;
    width: 100%;
    padding: 0;
    transition: all 0.5s;
  }

  .service-title h3 {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .service-title p {
    font-size: 12px;
    color: #999999;
  }

  .service-desc {
    font-size: 12px;
    color: #454545;
    line-height: 16px;
    margin-top: 12px;
  }

  .service-desc p {
    text-indent: 0;
    margin-bottom: 10px;
    text-align: justify;
  }

  .service-desc p::before {
    display: none;
  }

  .service-contact-button {
    background: transparent;
    line-height: 28px;
    font-size: 14px;
    border-radius: 25px;
    padding: 0 24px;
    border: 1px #000 solid;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-top: 16px;
    transition: all 0.3s;
  }

  .service-contact-button:hover {
    background-color: #000;
    color: white;
  }

  .inline-arrow {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }

  /* Advantages 移动端样式 */
  .advantages {
    max-width: 680px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .advantage-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .advantage-item {
    width: calc(50% - 8px);
    height: 130px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #333333;
    background-color: #FEEFDF;
    border-radius: 18px;
    margin-bottom: 16px;
    padding: 0;
    box-sizing: border-box;
    transition: all 0.4s;
  }

  .advantage-icon-area {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }

  .advantage-icon-area img {
    width: 32px;
    height: 32px;
  }

  .advantage-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 4px;
    line-height: 1.2;
  }

  .advantage-content {
    font-size: 12px;
    color: #666666;
    line-height: 1.3;
    padding: 0 8px;
    width: auto;
    margin: 0;
  }

  /* Lifecycle 移动端样式 */
  .lifecycle {
    max-width: 680px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-border;
  }

  .lifecycle-list {
    margin-bottom: 32px;
  }

  .lifecycle-list-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .lifecycle-item {
    text-align: center;
    position: static;
    left: auto;
    opacity: 1;
    width: calc(100% / 7 - 4px);
    margin-bottom: 16px;
  }

  .lifecycle-item-icon {
    background: #FFFFFF;
    height: 48px;
    width: 48px;
    border-radius: 50%;
    margin-bottom: 8px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px auto;
  }

  .lifecycle-item-icon img {
    width: 24px;
    height: 24px;
  }

  .lifecycle-item-icon .arrow {
    display: none;
  }

  .lifecycle-item-title {
    font-size: 10px;
    font-weight: 600;
    color: #333333;
    line-height: 1.2;
    max-width: none;
    margin: 0;
  }

  .lifecycle-repeat {
    width: 100%;
    margin: 0;
    text-align: center;
  }

  .lifecycle-repeat img {
    width: 100%;
    max-width: 400px;
  }

  /* Process 移动端样式 */
  .process {
    max-width: 680px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .process-list .process-item {
    position: static;
    min-width: auto;
    margin-bottom: 32px;
    height: auto;
  }

  .process-content-wrapper {
    width: 100%;
    margin: 0;
    display: block;
    height: auto;
  }

  .process-background {
    display: none;
  }

  .process-item .figure-area {
    flex: none;
    display: flex;
    justify-content: flex-start;
    position: static;
    top: auto;
    transition: none;
    opacity: 1;
    margin-bottom: 16px;
  }

  .process-item .figure-area img {
    width: 320px;
    height: 180px;
    border-radius: 8px;
    filter: none;
  }

  .process-content {
    margin-top: 0;
    width: 100%;
    flex: none;
    padding: 0;
  }

  .process-title h3 {
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 8px;
  }

  .process-title p {
    width: 100%;
    font-size: 12px;
    color: #666666;
    line-height: 1.4;
    margin-top: 8px;
  }

  .process-desc {
    font-size: 12px;
    color: #666666;
    line-height: 1.4;
  }

  /* Solution 移动端样式 */
  .solution {
    max-width: 680px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    min-width: auto;
    overflow: visible;
  }

  .solution-list {
    margin: 0;
  }

  .solution-item {
    padding: 32px 0;
    margin-bottom: 0;
    overflow: hidden;
    transition: all 0.5s;
  }

  .solution-wrapper {
    width: 100%;
    margin: 0;
    display: block;
  }

  .solution-figure {
    position: static;
    opacity: 1;
    min-height: auto;
    transition: all 0.5s;
    margin-bottom: 16px;
  }

  .solution-figure figure img {
    width: 100%;
    max-width: 320px;
    height: auto;
  }

  .solution-content {
    position: static;
    opacity: 1;
    width: 100%;
    flex: none;
    transition: all 0.5s;
  }

  .solution-title h3 {
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 8px;
  }

  .solution-title p {
    font-size: 12px;
    color: #999;
    line-height: 1.3;
    margin: 4px 0 0 0;
  }

  .solution-desc {
    font-size: 12px;
    color: #333;
    line-height: 1.4;
    overflow: hidden;
    max-height: 315px;
    transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .solution-desc.expanded {
    max-height: 2000px;
  }

  .solution-desc h4 {
    margin: 12px 0 6px 0;
    font-weight: bold;
    font-size: 12px;
  }

  .solution-desc ol,
  .solution-desc ul {
    padding-left: 16px;
    margin: 8px 0;
  }

  .solution-desc li {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 4px;
  }

  .solution-desc p {
    margin: 8px 0;
    font-size: 12px;
    line-height: 1.4;
  }

  .solution-expand {
    margin-top: 12px;
  }

  .solution-toggle {
    background: transparent;
    border: 1px #000 solid;
    padding: 0 16px;
    line-height: 28px;
    border-radius: 15px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    color: #000;
    font-size: 14px;
    transition: all 0.3s;
  }

  .solution-toggle:hover {
    background-color: #000;
    color: white;
  }

  .solution-toggle .inline-arrow {
    width: 12px;
    height: 12px;
    margin-left: 4px;
  }

  /* 移动端背景样式 */
  .solution-list .case1-bg,
  .solution-list .case2-bg,
  .solution-list .case3-bg,
  .solution-list .case4-bg,
  .solution-list .case5-bg,
  .solution-list .case6-bg {
    background: none;
  }
}

/* Customer List 样式 */
.customer-item {
  width: 12.5%;
  height: 55px;
  margin-bottom: 15px;
  padding: 0 4px;
}

.customer-item figure {
  width: 100%;
  height: 100%;
}

.customer-item img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-banner-text .title {
    font-size: 2.5rem;
  }
  
  .header-banner-text .slogon {
    font-size: 1.75rem;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .service-title h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .header-banner {
    padding: 2rem 0 4rem 0;
  }
  
  .header-banner-text .title {
    font-size: 2rem;
  }
  
  .header-banner-text .slogon {
    font-size: 1.5rem;
  }
  
  .header-banner-image {
    height: 300px;
    margin-top: 2rem;
  }
  
  .section-title h2 {
    font-size: 1.75rem;
  }
  
  .service-title h3 {
    font-size: 1.5rem;
  }
  
  .lifecycle-list {
    flex-direction: column;
    gap: 1rem;
  }
  
  .lifecycle-item-icon .arrow {
    display: none;
  }
  
  .customer-item {
    width: 25%;
  }
}

@media (max-width: 640px) {
  .header-banner-text .title {
    font-size: 1.75rem;
  }
  
  .header-banner-text .desc {
    font-size: 1rem;
  }
  
  .section-title h2 {
    font-size: 1.5rem;
  }
  
  .service-title h3 {
    font-size: 1.25rem;
  }
  
  .advantage-list {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .customer-item {
    width: 50%;
  }
}

/* Calculator Page Styles */
.contries-page {
  font-family: Helvetica;
  min-height: 100vh;
}

.contries-page header {
  background: #fff6ec;
}

.contries-page .header-banner {
  position: relative;
  width: 1204px;
  height: 610px;
  margin: 0 auto;
  background-image: url("/images/calculator/banner.png");
  background-size: 1204px 590px;
  background-position: 0px;
  background-repeat: no-repeat;
}

.contries-page .header-banner-text {
  position: absolute;
  left: 0;
  width: 641px;
  margin-top: 170px;
}

.contries-page .header-title {
  font-size: 46px;
  font-weight: bold;
  color: #170f49;
  margin: 0;
  line-height: 1.2;
}

.contries-page .header-desc {
  font-size: 18px;
  line-height: 30px;
  letter-spacing: 3px;
  font-weight: 300;
  color: #170f49;
  margin-top: 16px;
  width: 476px;
}

.contries-page .header-form {
  position: absolute;
  top: 80px;
  right: 0;
  width: 405px;
  background: #fff;
  border: 1px #eff0f6 solid;
  border-radius: 22px;
  box-sizing: border-box;
  box-shadow: 0 10px 14px 0 rgba(74, 58, 255, 0.01),
    0 9px 26px 0 rgba(23, 15, 73, 0.05);
  padding: 40px;
}

.contries-page .calculator-title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #170f49;
}

.contries-page .calculator-desc {
  text-align: center;
  font-size: 14px;
  color: #6f6c90;
  margin-bottom: 30px;
  line-height: 20px;
}

.contries-page .form-item {
  margin-bottom: 20px;
}

.contries-page .form-select,
.contries-page .form-input {
  width: 100%;
  height: 48px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 14px;
  color: #170f49;
  background: #fff;
  box-sizing: border-box;
}

.contries-page .form-select:focus,
.contries-page .form-input:focus {
  outline: none;
  border-color: #f54a25;
}

.contries-page .form-input.disabled {
  background: #f8f9fa;
  color: #6f6c90;
}

.contries-page .calculator-submit {
  color: #fff;
  width: 100%;
  height: 56px;
  line-height: 56px;
  border-radius: 50px;
  background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contries-page .calculator-submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 74, 37, 0.3);
}

.contries-page .calculator-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.contries-page .errorMessage {
  color: red;
  font-size: 14px;
  margin: 20px 0;
  cursor: pointer;
  text-align: center;
}

/* Calculator结果区域样式 */
.calculator-container {
  width: 1204px;
  margin: 0 auto;
  padding: 50px 0;
}

.calculator-container .result-title {
  margin-top: 50px;
  font-size: 36px;
  text-align: center;
  color: #170f49;
  font-weight: 600;
}

.calculator-container .result-description {
  font-size: 18px;
  text-align: center;
  color: #6f6c90;
  line-height: 30px;
  margin: 20px 0 40px;
}

.period-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.period-toggle button {
  padding: 10px 20px;
  margin: 0 5px;
  border: 1px solid #e1e5e9;
  background: #fff;
  color: #6f6c90;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.period-toggle button.active {
  background: #f54a25;
  color: #fff;
  border-color: #f54a25;
}

.result-table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 20px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-table tr,
.result-table th,
.result-table td {
  border: 1px #dbdee5 solid;
}

.result-table td {
  width: 50%;
  font-size: 14px;
  height: 28px;
  padding: 24px 30px;
  color: #170f49;
  font-weight: 500;
}

.result-table .total-row td {
  background: rgba(245, 74, 37, 0.1);
  font-weight: 600;
  color: #f54a25;
}

.result-notice {
  color: #6f6c90;
  font-size: 12px;
  line-height: 16px;
  margin-top: 15px;
  text-align: center;
}

.entity-notice {
  margin-top: 68px;
  font-size: 32px;
  font-weight: 400;
  color: #170f49;
  text-align: center;
}

.calculator-contact {
  display: block;
  cursor: pointer;
  font-size: 20px;
  font-weight: 500;
  text-align: center;
  border-radius: 50px;
  width: 290px;
  height: 72px;
  color: #fff;
  border: 0;
  background: linear-gradient(252deg, #f54a25 -45%, #ffab71 98%);
  box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
  margin: 20px auto 75px;
  line-height: 72px;
  transition: all 0.3s ease;
}

.calculator-contact:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 129, 42, 0.4);
}

/* Calculator移动端样式 */
@media (max-width: 768px) {
  .contries-page header {
    background: #fff6ec;
    padding: 20px 0;
  }

  .contries-page .header-banner {
    position: static;
    width: 100%;
    height: auto;
    margin: 0;
    background-image: none;
    background: transparent;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .contries-page .header-banner-text {
    position: static;
    width: 100%;
    margin-top: 0;
    text-align: center;
    margin-bottom: 30px;
  }

  .contries-page .header-title {
    font-size: 24px;
    margin-bottom: 16px;
  }

  .contries-page .header-desc {
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 1px;
    width: 100%;
  }

  .contries-page .header-form {
    position: static;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 30px 20px;
  }

  .calculator-container {
    width: 100%;
    padding: 30px 20px;
  }

  .calculator-container .result-title {
    font-size: 24px;
    margin-top: 20px;
  }

  .calculator-container .result-description {
    font-size: 14px;
    line-height: 20px;
  }

  .result-table td {
    padding: 16px 20px;
    font-size: 12px;
  }

  .entity-notice {
    font-size: 18px;
    margin-top: 40px;
  }

  .calculator-contact {
    width: 100%;
    max-width: 290px;
    height: 56px;
    line-height: 56px;
    font-size: 16px;
  }
}

.contries-page .calculate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Calculator Results */
.calculator-results {
  max-width: 1204px;
  margin: 60px auto;
  padding: 0 20px;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.result-table td {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.result-table tr:last-child td {
  border-bottom: none;
}

.cost-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  width: 60%;
}

.cost-amount {
  font-size: 18px;
  font-weight: bold;
  color: #ff8600;
  text-align: right;
}

.cost-name-detail {
  font-size: 14px;
  color: #666;
  padding-left: 40px;
}

.cost-amount-detail {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.cost-position {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cost-position .icon {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.periodChange {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-dropdown-time {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  background: white;
  font-size: 14px;
}

.entity-notice {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 40px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.result-notice {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  line-height: 1.4;
}

.calculator-contact {
  background: linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
  margin: 20px auto;
  display: block;
}

.calculator-contact:hover {
  transform: translateY(-1px);
}

.no-result {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
}

.result-title {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 16px;
}

.result-description {
  font-size: 18px;
  text-align: center;
  color: #666;
  margin-bottom: 48px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 8px;
}