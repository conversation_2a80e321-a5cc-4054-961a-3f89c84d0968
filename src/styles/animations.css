/* 高级动画效果 */

/* 旋转动画 */
@keyframes aniRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-rotate {
  animation: aniRotate 30s linear infinite !important;
  animation-fill-mode: forwards !important;
  animation-play-state: running !important;
}

.animate-rotate-slow {
  animation: aniRotate 18s linear infinite !important;
  animation-fill-mode: forwards !important;
  animation-play-state: running !important;
}

/* 确保旋转动画不被其他样式覆盖 */
.animate-rotate img,
.animate-rotate-slow img {
  animation: inherit !important;
}

/* Tailwind-like utility classes for positioning */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }

.-mt-\[300px\] { margin-top: -300px; }
.-ml-\[299px\] { margin-left: -299px; }
.-mt-\[240px\] { margin-top: -240px; }
.-ml-\[240px\] { margin-left: -240px; }

.w-\[598px\] { width: 598px; }
.h-\[600px\] { height: 600px; }
.w-\[480px\] { width: 480px; }
.h-\[480px\] { height: 480px; }

.top-\[19px\] { top: 19px; }
.-left-\[10px\] { left: -10px; }

.rotate-45 { transform: rotate(45deg); }
.min-h-\[600px\] { min-height: 600px; }

/* Service section specific styles */
.min-w-\[1280px\] { min-width: 1280px; }
.w-\[1204px\] { width: 1204px; }
.w-\[500px\] { width: 500px; }
.w-\[587px\] { width: 587px; }
.h-\[333px\] { height: 333px; }
.py-\[58px\] { padding-top: 58px; padding-bottom: 58px; }
.mt-\[43px\] { margin-top: 43px; }
.text-\[30px\] { font-size: 30px; }
.text-\[48px\] { font-size: 48px; }
.leading-\[50px\] { line-height: 50px; }
.leading-\[67px\] { line-height: 67px; }
.tracking-\[5px\] { letter-spacing: 5px; }
.rounded-\[20px\] { border-radius: 20px; }
.min-h-\[333px\] { min-height: 333px; }
.h-\[22px\] { height: 22px; }

/* Advantage section styles */
.w-\[279px\] { width: 279px; }
.h-\[251px\] { height: 251px; }
.bg-\[#FEEFDF\] { background-color: #FEEFDF; }
.rounded-\[18px\] { border-radius: 18px; }
.text-\[28px\] { font-size: 28px; }
.pt-12 { padding-top: 3rem; }
.pb-10 { padding-bottom: 2.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-3 { margin-top: 0.75rem; }
.duration-400 { transition-duration: 400ms; }
.scale-0 { transform: scale(0); }

/* Lifecycle section styles */
.w-\[1000px\] { width: 1000px; }
.w-\[152px\] { width: 152px; }
.h-\[152px\] { height: 152px; }
.mb-\[86px\] { margin-bottom: 86px; }
.mb-8 { margin-bottom: 2rem; }
.-left-10 { left: -2.5rem; }
.h-\[14px\] { height: 14px; }
.w-\[12px\] { width: 12px; }
.top-\[67px\] { top: 67px; }
.-right-\[18px\] { right: -18px; }
.bg-no-repeat { background-repeat: no-repeat; }
.bg-contain { background-size: contain; }

/* Process section styles */
.min-w-\[1280px\] { min-width: 1280px; }
.h-\[506px\] { height: 506px; }
.mb-\[160px\] { margin-bottom: 160px; }
.w-\[1253px\] { width: 1253px; }
.w-1\/2 { width: 50%; }
.z-\[-1\] { z-index: -1; }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-l { background-image: linear-gradient(to left, var(--tw-gradient-stops)); }
.from-white { --tw-gradient-from: #ffffff; --tw-gradient-to: rgb(255 255 255 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-\[#FFF2E3\] { --tw-gradient-to: #FFF2E3; }
.text-\[64px\] { font-size: 64px; }
.top-\[100px\] { top: 100px; }
.left-\[610px\] { left: 610px; }
.right-\[610px\] { right: 610px; }
.font-verdana { font-family: Verdana, sans-serif; }
.leading-none { line-height: 1; }
.-top-\[46px\] { top: -46px; }
.top-10 { top: 2.5rem; }
.duration-800 { transition-duration: 800ms; }

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-slow {
  animation: floatSlow 4s ease-in-out infinite;
}

/* 淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
  opacity: 0;
  animation-delay: 0.2s;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
  opacity: 0;
  animation-delay: 0.2s;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
  opacity: 1;
}

/* 通用动画触发类 - 模拟v1的滚动动画效果 */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) translateX(0) scale(1) !important;
  transition: all 0.8s ease-out !important;
}

/* v1风格的滚动动画初始状态 */
.opacity-0 {
  opacity: 0 !important;
}

.translate-y-8 {
  transform: translateY(2rem) !important;
}

.transition-all {
  transition-property: all !important;
}

.duration-800 {
  transition-duration: 800ms !important;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important;
}

/* Service项目的左右滑入动画 */
.service-item {
  transition: all 0.5s ease-out !important;
}

.service-item.animate-in {
  opacity: 1 !important;
  transform: translateX(0) !important;
}

/* 服务项目内容和图片的独立动画 */
.service-content.animate-in {
  opacity: 1 !important;
  transform: translateX(0) translateY(0) !important;
}

.figure-area.animate-in {
  opacity: 1 !important;
  transform: translateX(0) translateY(0) !important;
}

/* 优化动画缓动效果 */
.service-content,
.figure-area {
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 确保动画触发时的平滑过渡 */
.service-content.animate-in,
.figure-area.animate-in {
  opacity: 1 !important;
  transform: translateX(0) translateY(0) !important;
}

/* 服务项目悬停效果 */
.service-item:hover .figure-area figure {
  transform: scale(1.02);
  transition: transform 0.3s ease-out;
}

.service-item .figure-area figure {
  transition: transform 0.3s ease-out;
}

/* 按钮悬停效果增强 */
.service-contact-button {
  position: relative;
  overflow: hidden;
}

.service-contact-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.service-contact-button:hover::before {
  left: 100%;
}

/* 首屏背景渐变增强 */
.header-banner {
  position: relative;
  overflow: hidden;
}

.header-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(249, 115, 22, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* 服务部分的背景增强 */
.service {
  position: relative;
}


.service > * {
  position: relative;
  z-index: 1;
}

/* 图片容器的阴影效果 */
.figure-area figure {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-out, transform 0.3s ease-out;
}

.service-item:hover .figure-area figure {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 优势项目动画增强 */
.advantage-item.animate-in {
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* 生命周期项目动画增强 */
.lifecycle-item.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* 流程项目动画增强 */
.process-content.animate-in,
.process-item .figure-area.animate-in {
  opacity: 1 !important;
  transform: translateX(0) !important;
}

/* 解决方案项目动画增强 */
.solution-content.animate-in,
.solution-figure.animate-in {
  opacity: 1 !important;
  transform: translateX(0) !important;
}

/* 全局动画优化 */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) translateX(0) scale(1) !important;
}

/* 页面滚动平滑 */
html {
  scroll-behavior: smooth;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .service-content,
  .figure-area {
    transform: none !important;
    opacity: 1 !important;
  }
}

/* 初始状态 */
.animate-fade-in-up:not(.animate-in) {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-fade-in-right:not(.animate-in) {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-fade-in-left:not(.animate-in) {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 15s ease infinite;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 缓慢浮动 */
@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

/* 摇摆动画 */
@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

.animate-wiggle {
  animation: wiggle 2s ease-in-out infinite;
}

/* 心跳动画 */
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* 打字机光标闪烁 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

/* 滑入动画 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.6s ease-out;
}

/* 缩放动画 */
@keyframes zoomIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes zoomOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0);
    opacity: 0;
  }
}

.animate-zoom-in {
  animation: zoomIn 0.6s ease-out;
}

.animate-zoom-out {
  animation: zoomOut 0.6s ease-out;
}

/* 旋转动画 */
@keyframes rotateIn {
  from {
    transform: rotate(-200deg);
    opacity: 0;
  }
  to {
    transform: rotate(0);
    opacity: 1;
  }
}

.animate-rotate-in {
  animation: rotateIn 0.6s ease-out;
}

/* 翻转动画 */
@keyframes flipInX {
  from {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateX(-20deg);
  }
  60% {
    transform: perspective(400px) rotateX(10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotateX(-5deg);
  }
  to {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
}

@keyframes flipInY {
  from {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-20deg);
  }
  60% {
    transform: perspective(400px) rotateY(10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotateY(-5deg);
  }
  to {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

.animate-flip-in-x {
  animation: flipInX 0.75s ease-out;
}

.animate-flip-in-y {
  animation: flipInY 0.75s ease-out;
}

/* 弹跳动画 */
@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

.animate-bounce-in {
  animation: bounceIn 0.75s ease-out;
}

/* 光晕效果 */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(249, 115, 22, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.8), 0 0 30px rgba(249, 115, 22, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 渐变文字动画 */
@keyframes gradient-text {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-text {
  background: linear-gradient(-45deg, #ee7724, #d8363a, #dd3675, #b44593);
  background-size: 400% 400%;
  animation: gradient-text 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 波浪动画 */
@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  10%, 30% {
    transform: rotate(14deg);
  }
  20% {
    transform: rotate(-8deg);
  }
  40% {
    transform: rotate(-4deg);
  }
  50% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(0deg);
  }
}

.animate-wave {
  animation: wave 2s ease-in-out infinite;
  transform-origin: 70% 70%;
}

/* 延迟动画类 */
.animate-delay-75 {
  animation-delay: 75ms;
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-150 {
  animation-delay: 150ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

.animate-delay-700 {
  animation-delay: 700ms;
}

.animate-delay-1000 {
  animation-delay: 1000ms;
}

/* 持续时间类 */
.animate-duration-75 {
  animation-duration: 75ms;
}

.animate-duration-100 {
  animation-duration: 100ms;
}

.animate-duration-150 {
  animation-duration: 150ms;
}

.animate-duration-200 {
  animation-duration: 200ms;
}

.animate-duration-300 {
  animation-duration: 300ms;
}

.animate-duration-500 {
  animation-duration: 500ms;
}

.animate-duration-700 {
  animation-duration: 700ms;
}

.animate-duration-1000 {
  animation-duration: 1000ms;
}

/* 悬停效果 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.6);
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
