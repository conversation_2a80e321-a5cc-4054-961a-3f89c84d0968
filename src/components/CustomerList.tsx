'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

const CustomerList: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [customerList, setCustomerList] = useState<string[][]>([]);
  const [prevShow, setPrevShow] = useState(false);
  const [nextShow, setNextShow] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 客户logo列表
    const list = [
      "249.png","250.png","251.png",
      "220.png","221.png","222.png","223.png","224.png","225.png","226.png","227.png",
      "228.png","229.png","230.png","231.png","232.png","233.png","234.png","235.png",
      "236.png","237.png","238.png","239.png","240.png","241.png","242.png","243.png",
      "244.png","245.png","246.png","247.png","248.png","175.png","176.png","177.png","178.png","179.png","180.png","181.png","182.png",
      "183.png","184.png","185.png","186.png","187.png","188.png","189.png","190.png",
      "191.png","192.png","193.png","194.png","195.png","196.png","197.png","198.png",
      "199.png","200.png","201.png","202.png","203.png","204.png","205.png","206.png",
      "207.png","208.png","209.png","210.png","211.png","212.png","213.png","214.png",
      "215.png","216.png","217.png","218.png","219.png","1-1.png","1-2.png","1-3.png",
      "1-4.png","1-5.png","1-6.png","1-7.png","1-8.png","1-9.png","2.png","3.png","4.png",
      "7.png","8.png","9.png","11.png","14.png","15.png","16.png","17.png","18.png","19.png",
      "20.png","22.png","23.png","24.png","27.png","28.png","29.png","31.png","34.png","35.png",
      "36.png","37.png","38.png","39.png","41.png","43.png","44.png","45.png","46.png","48.png",
      "49.png","50.png","51.png","52.png","54.png","55.png","56.png","60.png","61.png","62.png",
      "64.png","65.png","66.png","67.png","69.png","70.png","71.png","72.png","74.png","75.png",
      "76.png","77.png","79.png","81.png","83.png","84.png","86.png","88.png","89.png","91.png",
      "92.png","93.png","95.png","96.png","97.png","98.png","100.png","101.png","102.png",
      "103.png","105.png","107.png","109.png","110.png","112.png","114.png","115.png","116.png",
      "117.png","120.png","122.png","123.png","125.png","126.png","129.png","130.png","131.png",
      "133.png","135.png","136.png","137.png","138.png","139.png","140.png","142.png","143.png",
      "144.png","145.png","146.png","148.png","149.png","150.png","151.png","152.png","154.png",
      "155.png","156.png","157.png","160.png","161.png","162.png","163.png","164.png","165.png",
      "166.png","167.png","168.png","169.png","170.png","171.png","172.png","173.png","174.png"
    ];
    
    // 每页32个logo，分组
    const result: string[][] = []; 
    for (let i = 0; i < list.length; i += 32) {  
      result.push(list.slice(i, i + 32));  
    }

    setCustomerList(result);
  }, []);

  // 自动轮播
  useEffect(() => {
    if (customerList.length > 0) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % customerList.length);
      }, 2500);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [customerList.length]);

  const handleClickArrow = (direction: 'left' | 'right') => {
    if (direction === 'left') {
      setCurrentSlide(prev => prev === 0 ? customerList.length - 1 : prev - 1);
    } else {
      setCurrentSlide(prev => (prev + 1) % customerList.length);
    }
  };

  const handleMouseEnter = (direction: 'left' | 'right') => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (direction === 'left') {
      setPrevShow(true);
    } else {
      setNextShow(true);
    }
  };

  const handleMouseLeave = (direction: 'left' | 'right') => {
    // 重新开始自动轮播
    if (customerList.length > 0) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % customerList.length);
      }, 2500);
    }
    
    if (direction === 'left') {
      setPrevShow(false);
    } else {
      setNextShow(false);
    }
  };

  if (customerList.length === 0) {
    return <div>Loading...</div>;
  }

  return (
    <div className="carousel relative group">
      {/* 轮播内容 */}
      <div className="overflow-hidden h-[270px]">
        <div 
          className="flex transition-transform duration-500 ease-in-out h-full"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {customerList.map((slideLogos, slideIndex) => (
            <div key={slideIndex} className="w-full flex-shrink-0 flex flex-wrap">
              {slideLogos.map((logo, logoIndex) => (
                <div key={logoIndex} className="customer-item w-1/8 h-[55px] mb-4 px-1">
                  <figure className="w-full h-full">
                    <Image
                      src={`/images/customer/${logo}`}
                      alt={`Customer ${logoIndex + 1}`}
                      width={120}
                      height={55}
                      className="w-full h-full object-contain select-none"
                      onError={(e) => {
                        // 如果图片加载失败，隐藏该元素
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </figure>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* 左箭头 */}
      <div 
        className="prev absolute left-[-87px] top-1/2 transform -translate-y-1/2 w-[100px] h-[100px] cursor-pointer hidden group-hover:block"
        onClick={() => handleClickArrow('left')}
        onMouseEnter={() => handleMouseEnter('left')}
        onMouseLeave={() => handleMouseLeave('left')}
      >
        <figure className="w-full h-full">
          {!prevShow ? (
            <Image
              src="/images/index/carousel-d.png"
              alt="Previous"
              width={100}
              height={100}
              className="w-full h-full"
            />
          ) : (
            <Image
              src="/images/index/carousel-h.png"
              alt="Previous"
              width={100}
              height={100}
              className="w-full h-full transform rotate-180"
              style={{ marginTop: '-3.5px' }}
            />
          )}
        </figure>
      </div>

      {/* 右箭头 */}
      <div 
        className="next absolute right-[-87px] top-1/2 transform -translate-y-1/2 rotate-180 w-[100px] h-[100px] cursor-pointer hidden group-hover:block"
        onClick={() => handleClickArrow('right')}
        onMouseEnter={() => handleMouseEnter('right')}
        onMouseLeave={() => handleMouseLeave('right')}
      >
        <figure className="w-full h-full">
          {!nextShow ? (
            <Image
              src="/images/index/carousel-d.png"
              alt="Next"
              width={100}
              height={100}
              className="w-full h-full"
            />
          ) : (
            <Image
              src="/images/index/carousel-h.png"
              alt="Next"
              width={100}
              height={100}
              className="w-full h-full transform rotate-180"
              style={{ marginTop: '-3.5px' }}
            />
          )}
        </figure>
      </div>

      {/* 指示器 */}
      <div className="flex justify-center mt-4 space-x-2">
        {customerList.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-colors ${
              index === currentSlide ? 'bg-orange-500' : 'bg-gray-300'
            }`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default CustomerList;