'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '@/types';
import { switchLanguage } from '@/utils/lang';

interface FooterProps {
  locale: Locale;
  onShowContactForm?: () => void;
}

const Footer: React.FC<FooterProps> = ({ locale, onShowContactForm }) => {
  const [showRecruitmentDialog, setShowRecruitmentDialog] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  const handleEmailClick = () => {
    window.location.href = 'mailto:<EMAIL>';
  };

  const handleExternalLink = (url: string) => {
    window.open(url, '_blank');
  };

  const handleLanguageSwitch = (newLocale: Locale) => {
    switchLanguage(newLocale);
    setShowLanguageDropdown(false);
  };

  const getContent = () => {
    const content = {
      zh: {
        email: '<EMAIL>',
        recruitment: '灵鹿聘·全球人才招聘',
        aboutUs: '关于我们',
        contactUs: '联系我们',
        joinCommunity: '加入社区',
        copyright: '© 2022 SMARTDEER',
        companyName: '北京谊桥管理科技有限责任公司版权所有',
        icp: '京ICP备**********号-1',
        hrLicense: '人力资源许可证',
        businessLicense: '营业执照',
        policeRecord: '京公网安备 11010102005860 号',
        language: '中文',
        recruitmentDialog: {
          title: '灵鹿聘·全球人才招聘',
          socialRecruitment: '全球社招',
          campusRecruitment: '全球校招',
          wechatText: '请使用微信扫描二维码',
          websiteLink: 'http://www.smartdeer.co/'
        }
      },
      en: {
        email: '<EMAIL>',
        recruitment: 'Smartdeer Global Recruitment',
        aboutUs: 'About Us',
        contactUs: 'Contact Us',
        joinCommunity: 'Join the community',
        copyright: 'Copyright© 2022 Beijing Yiqiao LLC All Rights Reserved',
        companyName: '',
        icp: '',
        hrLicense: 'Human Resources License',
        businessLicense: 'Business License',
        policeRecord: '',
        language: 'English',
        recruitmentDialog: {
          title: 'Smartdeer Global Recruitment',
          socialRecruitment: 'For White Collar Recruitment',
          campusRecruitment: 'For Student Recruitment',
          wechatText: 'Please use WeChat to scan the QR code',
          websiteLink: 'http://www.smartdeer.co/'
        }
      },
      ja: {
        email: '<EMAIL>',
        recruitment: 'サマートディアー',
        aboutUs: '会社概要',
        contactUs: 'お問い合わせ',
        joinCommunity: 'コミュニティ参加',
        copyright: '© 2025 北京谊桥管理科技有限責任公司 版権所有',
        companyName: '',
        icp: '',
        hrLicense: '人材派遣許可証',
        businessLicense: '事業許可証',
        policeRecord: '',
        language: '日本語',
        recruitmentDialog: {
          title: 'サマートディアー',
          socialRecruitment: 'グローバル社招',
          campusRecruitment: 'グローバル校招',
          wechatText: 'ウェブサイトを開く',
          websiteLink: 'http://www.smartdeer.co/'
        }
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  return (
    <>
      <footer className="bg-gray-800 text-white py-12 lg:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Logo */}
              <div>
                <Image
                  src="/images/index/footer-logo.png"
                  alt="SmartDeer"
                  width={219}
                  height={44}
                  className="h-11 w-auto"
                />
              </div>

              {/* Email */}
              <div>
                <button
                  onClick={handleEmailClick}
                  className="text-lg hover:text-orange-500 transition-colors"
                >
                  {content.email}
                </button>
              </div>

              {/* Links */}
              <div className="flex flex-wrap gap-6 lg:gap-12">
                <button
                  onClick={() => handleExternalLink('https://www.smartdeer.com/')}
                  className="text-lg hover:text-orange-500 transition-colors cursor-pointer"
                >
                  {content.recruitment}
                </button>
                <Link
                  href={`/${locale}/aboutus`}
                  className="text-lg hover:text-orange-500 transition-colors"
                >
                  {content.aboutUs}
                </Link>
                <button
                  onClick={onShowContactForm}
                  className="text-lg hover:text-orange-500 transition-colors"
                >
                  {content.contactUs}
                </button>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6 lg:text-right">
              {/* Compliance Logos */}
              <div className="flex justify-start lg:justify-end space-x-5">
                <Image
                  src="/images/index/iso27001.png"
                  alt="ISO 27001"
                  width={70}
                  height={70}
                />
                <Image
                  src="/images/index/cyberport.png"
                  alt="Cyberport"
                  width={70}
                  height={70}
                />
              </div>

              {/* Social Media */}
              <div className="space-y-3">
                <div className="text-lg font-light text-gray-400">
                  {content.joinCommunity}
                </div>
                <div className="flex justify-start lg:justify-end space-x-4">
                  <button
                    onClick={() => handleExternalLink('https://twitter.com/SmartDeerGlobal')}
                    className="group"
                  >
                    <Image
                      src="/images/index/twitter.svg"
                      alt="Twitter"
                      width={40}
                      height={40}
                      className="group-hover:hidden"
                    />
                    <Image
                      src="/images/index/twitter-h.svg"
                      alt="Twitter"
                      width={40}
                      height={40}
                      className="hidden group-hover:block"
                    />
                  </button>
                  <button
                    onClick={() => handleExternalLink('https://t.me/SmartDeerGlobal')}
                    className="group"
                  >
                    <Image
                      src="/images/index/telegram.svg"
                      alt="Telegram"
                      width={40}
                      height={40}
                      className="group-hover:hidden"
                    />
                    <Image
                      src="/images/index/telegram-h.svg"
                      alt="Telegram"
                      width={40}
                      height={40}
                      className="hidden group-hover:block"
                    />
                  </button>
                  <button
                    onClick={() => handleExternalLink('https://www.linkedin.com/company/smartdeer-global')}
                    className="group"
                  >
                    <Image
                      src="/images/index/LinkedIn.svg"
                      alt="LinkedIn"
                      width={40}
                      height={40}
                      className="group-hover:hidden"
                    />
                    <Image
                      src="/images/index/LinkedIn-h.svg"
                      alt="LinkedIn"
                      width={40}
                      height={40}
                      className="hidden group-hover:block"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-600 my-8"></div>

          {/* Bottom Section */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 relative">
            {/* Copyright and Links */}
            <div className="space-y-2 text-gray-400">
              <div className="flex flex-wrap gap-4">
                <span>{content.copyright}</span>
                {content.companyName && <span>{content.companyName}</span>}
              </div>
              <div className="flex flex-wrap gap-4 text-sm">
                {content.icp && (
                  <Link href="https://beian.miit.gov.cn/" className="hover:text-orange-500 transition-colors">
                    {content.icp}
                  </Link>
                )}
                <Link href="/compliance/hr-service-license" className="hover:text-orange-500 transition-colors">
                  {content.hrLicense}
                </Link>
                <Link href="/compliance/business-license" className="hover:text-orange-500 transition-colors">
                  {content.businessLicense}
                </Link>
                {content.policeRecord && (
                  <Link href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010102005860" className="hover:text-orange-500 transition-colors">
                    {content.policeRecord}
                  </Link>
                )}
              </div>
            </div>

            {/* Language Selector */}
            <div className="relative">
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center justify-center w-28 h-11 border-2 border-white rounded text-white hover:border-orange-500 hover:text-orange-500 transition-colors"
              >
                <span className="mr-2">{content.language}</span>
                <Image
                  src="/images/index/trangle.svg"
                  alt="Dropdown"
                  width={8}
                  height={8}
                />
              </button>
              
              {showLanguageDropdown && (
                <div className="absolute bottom-full right-0 mb-2 bg-white border border-gray-200 rounded shadow-lg z-50">
                  <button
                    onClick={() => handleLanguageSwitch('zh')}
                    className="block w-full px-4 py-2 text-left text-gray-800 hover:bg-gray-100 transition-colors"
                  >
                    中文
                  </button>
                  <button
                    onClick={() => handleLanguageSwitch('en')}
                    className="block w-full px-4 py-2 text-left text-gray-800 hover:bg-gray-100 transition-colors"
                  >
                    English
                  </button>
                  <button
                    onClick={() => handleLanguageSwitch('ja')}
                    className="block w-full px-4 py-2 text-left text-gray-800 hover:bg-gray-100 transition-colors"
                  >
                    日本語
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </footer>

      {/* Recruitment Dialog */}
      {showRecruitmentDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl p-8 relative">
            <button
              onClick={() => setShowRecruitmentDialog(false)}
              className="absolute top-4 right-4 p-2"
            >
              <Image
                src="/images/index/close.svg"
                alt="Close"
                width={14}
                height={14}
              />
            </button>
            
            <h2 className="text-2xl lg:text-3xl font-medium text-center text-gray-800 mb-12">
              {content.recruitmentDialog.title}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Social Recruitment */}
              <div className="text-center">
                <h3 className="text-sm font-bold text-gray-600 mb-4">
                  {content.recruitmentDialog.socialRecruitment}
                </h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-3">
                  <div className="w-24 h-24 mx-auto border border-gray-800 rounded-lg p-1 bg-white">
                    <Image
                      src="/images/index/wechat-qr.jpg"
                      alt="WeChat QR"
                      width={94}
                      height={94}
                      className="w-full h-full rounded"
                    />
                  </div>
                  <p className="text-xs text-gray-600">
                    {content.recruitmentDialog.wechatText}
                  </p>
                </div>
              </div>
              
              {/* Campus Recruitment */}
              <div className="text-center">
                <h3 className="text-sm font-bold text-gray-600 mb-4">
                  {content.recruitmentDialog.campusRecruitment}
                </h3>
                <div 
                  className="bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-3 cursor-pointer hover:border-orange-500 transition-colors"
                  onClick={() => handleExternalLink(content.recruitmentDialog.websiteLink)}
                >
                  <div className="w-24 h-24 mx-auto">
                    <Image
                      src="/images/index/guide-pc.svg"
                      alt="Website"
                      width={100}
                      height={100}
                      className="w-full h-full"
                    />
                  </div>
                  <p className="text-xs text-gray-600 underline">
                    {content.recruitmentDialog.websiteLink}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Footer;