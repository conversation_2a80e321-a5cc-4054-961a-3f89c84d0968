'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { Locale } from '@/types';
import { switchLanguage } from '@/utils/lang';
import { scrollToElement } from '@/utils';

interface HeaderProps {
  locale: Locale;
  showContactUs?: boolean;
  onShowContactForm?: () => void;
}

interface NavigationItem {
  label: string;
  href?: string;
  onClick?: () => void;
  isDropdown?: boolean;
  isCalculator?: boolean;
  children?: Array<{
    label: string;
    href: string;
  }>;
}

const Header: React.FC<HeaderProps> = ({ 
  locale, 
  showContactUs = true, 
  onShowContactForm 
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [showCountriesDropdown, setShowCountriesDropdown] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const countriesRef = useRef<HTMLDivElement>(null);
  const languageRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (countriesRef.current && !countriesRef.current.contains(event.target as Node)) {
        setShowCountriesDropdown(false);
      }
      if (languageRef.current && !languageRef.current.contains(event.target as Node)) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleScrollTo = (selector: string) => {
    const isHomePage = pathname === `/${locale}` || pathname === '/';
    
    if (isHomePage) {
      scrollToElement(selector);
    } else {
      router.push(`/${locale}/?scroll=${selector.substring(1)}`);
    }
  };

  const handleLanguageSwitch = (newLocale: Locale) => {
    switchLanguage(newLocale);
    setShowLanguageDropdown(false);
  };

  const handleCalculatorClick = () => {
    window.location.href = `/${locale}/calculator`;
  };

  const getNavigationItems = (): NavigationItem[] => {
    const items = {
      zh: [
        { label: '全球人力资源服务', onClick: () => handleScrollTo('#service-recruitment') },
        { label: '行业案例', onClick: () => handleScrollTo('#solution') },
        { label: '雇主成本计算器', onClick: handleCalculatorClick, isCalculator: true },
        {
          label: '资源库',
          isDropdown: true,
          children: [
            { label: '国家雇佣指南', href: `/${locale}/countries/` },
            { label: '媒体报道&市场活动', href: `/${locale}/marketing/` },
            { label: '文章', href: `/${locale}/articles/` }
          ]
        }
      ],
      en: [
        { label: 'Global HR Services', onClick: () => handleScrollTo('#service-recruitment') },
        { label: 'Success Stories', onClick: () => handleScrollTo('#solution') },
        { label: 'Employer Cost Calculator', onClick: handleCalculatorClick, isCalculator: true },
        {
          label: 'Resources',
          isDropdown: true,
          children: [
            { label: 'Global Hiring Guide', href: `/${locale}/countries/` },
            { label: 'Media & Marketing Activity', href: `/${locale}/marketing/` },
            { label: 'Articles', href: `/${locale}/articles/` }
          ]
        }
      ],
      ja: [
        { label: '海外人事サービス', onClick: () => handleScrollTo('#service-recruitment') },
        { label: '導入事例', onClick: () => handleScrollTo('#solution') },
        { label: '採用コスト計算ツール', href: `/${locale}/calculator` },
        { 
          label: 'お役立ち資料', 
          isDropdown: true,
          children: [
            { label: '国家雇佣指南', href: `/${locale}/countries/` },
            { label: 'メディア&マーケティング活動', href: `/${locale}/marketing/` },
            { label: 'お役立ち資料', href: `/${locale}/articles/` }
          ]
        }
      ]
    };
    
    return items[locale] || items.zh;
  };

  const getContactButtonText = () => {
    const texts = {
      zh: '联系我们',
      en: 'Contact Us',
      ja: 'お問い合わせ'
    };
    return texts[locale] || texts.zh;
  };



  return (
    <header className="site-header relative z-50 bg-[#fff6ec]">
      <div className="max-w-7xl mx-auto ">
        <div className="flex items-center justify-between py-6 lg:py-10">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex-shrink-0">
            <Image
              src="/images/index/sd_logo.png"
              alt="SmartDeer"
              width={219}
              height={44}
              className="h-8 lg:h-11 w-auto"
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 xl:space-x-14">
            {getNavigationItems().map((item, index) => (
              <div key={index} className="relative">
                {item.isDropdown ? (
                  <div ref={countriesRef}>
                    <button
                      onClick={() => setShowCountriesDropdown(!showCountriesDropdown)}
                      className="flex items-center space-x-2 text-base font-bold hover:text-orange-500 transition-colors"
                    >
                      <span>{item.label}</span>
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                    
                    {showCountriesDropdown && (
                      <div className="absolute top-full right-0 mt-2 w-96 bg-gray-800 text-white p-8 rounded shadow-lg z-50">
                        <div className="flex justify-between">
                          <div className="w-44">
                            <h3 className="text-base font-bold mb-3">
                              {locale === 'zh' ? '全球雇佣' : locale === 'en' ? 'COUNTRIES WE SERVE' : 'カバー国'}
                            </h3>
                            <p className="text-sm leading-relaxed">
                              {locale === 'zh' 
                                ? '掌握全球各国家地区人力资源法律法规，安全且高效地完成全球人才招聘与雇佣，为顺利开展全球化业务奠定坚实的基础。'
                                : locale === 'en'
                                ? 'Confidently engage talent 150+ countries with expert insights into local laws and regulations.'
                                : '150+ 国以上の専門家による法令や規制の専門知識を活用して、テレワークワーカーを安全かつ効率的に採用できます。'
                              }
                            </p>
                          </div>
                          <div className="flex flex-col space-y-2">
                            {item.children?.map((child, childIndex) => (
                              <Link
                                key={childIndex}
                                href={child.href || '#'}
                                className="block px-2 py-2 text-sm font-bold hover:bg-gray-600 hover:text-orange-500 transition-colors rounded"
                                onClick={() => setShowCountriesDropdown(false)}
                              >
                                {child.label}
                              </Link>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  item.href ? (
                    <Link
                      href={item.href}
                      className="text-base font-bold text-gray-900 hover:text-orange-500 transition-colors"
                      style={{ color: '#111827', textDecoration: 'none' }}
                    >
                      {item.label}
                    </Link>
                  ) : (
                    <button
                      onClick={item.onClick}
                      className={`text-base font-bold transition-colors ${
                        'text-gray-900 hover:text-orange-500'
                      }`}
                    >
                      {item.label}
                    </button>
                  )
                )}
              </div>
            ))}
          </nav>

          {/* Right side - Language & Contact */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative" ref={languageRef}>
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center space-x-2"
              >
                <Image
                  src="/images/index/icon_earth.png"
                  alt="Language"
                  width={26}
                  height={26}
                  className="cursor-pointer"
                />
              </button>
              
              {showLanguageDropdown && (
                <div className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded shadow-lg z-50">
                  <button
                    onClick={() => handleLanguageSwitch('zh')}
                    className="block w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors"
                  >
                    中文
                  </button>
                  <button
                    onClick={() => handleLanguageSwitch('en')}
                    className="block w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors"
                  >
                    English
                  </button>
                  <button
                    onClick={() => handleLanguageSwitch('ja')}
                    className="block w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors"
                  >
                    日本語
                  </button>
                </div>
              )}
            </div>

            {/* Contact Button */}
            {showContactUs && (
              <button
                onClick={onShowContactForm}
                className="hidden lg:block px-6 py-2 bg-gray-800 text-white text-sm font-bold rounded-full hover:bg-orange-500 transition-colors"
              >
                {getContactButtonText()}
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 py-4">
            <div className="flex flex-col space-y-4">
              {getNavigationItems().map((item, index) => (
                <div key={index}>
                  {item.href ? (
                    <Link
                      href={item.href}
                      className="block py-2 text-base font-bold hover:text-orange-500 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  ) : item.isDropdown ? (
                    <div>
                      <div className="py-2 text-base font-bold">{item.label}</div>
                      <div className="ml-4 space-y-2">
                        {item.children?.map((child, childIndex) => (
                          <Link
                            key={childIndex}
                            href={child.href}
                            className="block py-1 text-sm hover:text-orange-500 transition-colors"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={() => {
                        item.onClick?.();
                        setIsMobileMenuOpen(false);
                      }}
                      className={`block py-2 text-base font-bold transition-colors text-left ${
                        item.isCalculator
                          ? 'hover:text-yellow-500'
                          : 'hover:text-orange-500'
                      }`}
                    >
                      {item.label}
                    </button>
                  )}
                </div>
              ))}
              
              {showContactUs && (
                <button
                  onClick={() => {
                    onShowContactForm?.();
                    setIsMobileMenuOpen(false);
                  }}
                  className="mt-4 px-6 py-2 bg-gray-800 text-white text-sm font-bold rounded-full hover:bg-orange-500 transition-colors text-center"
                >
                  {getContactButtonText()}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;