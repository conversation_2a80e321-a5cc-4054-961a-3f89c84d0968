'use client';

import React, { useState } from 'react';
import { Locale } from '@/types';
import ContactForm from '@/components/ContactForm';

interface ArticleDetailClientProps {
  locale: Locale;
  confirmPrompt: string;
}

export default function ArticleDetailClient({ locale, confirmPrompt }: ArticleDetailClientProps) {
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleSubmitSuccess = () => {
    setShowSuccessMessage(true);
    // 显示成功消息3秒后隐藏
    setTimeout(() => {
      setShowSuccessMessage(false);
    }, 3000);
  };

  const handleScrollToTop = () => {
    if (typeof window !== 'undefined') {
      document.documentElement.scrollTop = 0;
    }
  };

  return (
    <div className="article-detail-client">
      {/* Contact Form */}
      <ContactForm 
        locale={locale}
        onSubmitSuccess={handleSubmitSuccess}
        textareaResize="none"
      />
      
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="success-message">
          {confirmPrompt}
        </div>
      )}
    </div>
  );
}
